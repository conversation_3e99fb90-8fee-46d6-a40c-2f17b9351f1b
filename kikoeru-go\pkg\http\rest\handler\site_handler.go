package handler

import (
	"net/http"

	"log/slog"

	"github.com/Sakura-Byte/kikoeru-go/pkg/config"
	"github.com/Sakura-Byte/kikoeru-go/pkg/http/rest/common"
	"github.com/gin-gonic/gin"
)

// SiteInfoResponse defines the structure for site configuration information exposed to the frontend.
type SiteInfoResponse struct {
	EnableEmailFeatures bool `json:"enable_email_features"`
	// EnsureEmail indicates if an email is mandatory during registration and requires verification.
	// - If true (and EnableEmailFeatures is true): Email is required at registration. A verification email will be sent.
	// - If false (and EnableEmailFeatures is true): Email will not be inputed at registration. User can link email later.
	// - This setting has no effect if EnableEmailFeatures is false.
	EnsureEmail       bool `json:"ensure_email"`
	AllowRegistration bool `json:"allow_registration"`
	// Add other relevant public-facing configurations here as needed
}

// SiteHandler handles requests related to site-wide information.
type SiteHandler struct {
	appConfig *config.AppConfig
	logger    *slog.Logger
}

// NewSiteHandler creates a new SiteHandler.
func NewSiteHandler(appConfig *config.AppConfig) *SiteHandler {
	return &SiteHandler{
		appConfig: appConfig,
		logger:    slog.Default().With("handler", "SiteHandler"),
	}
}

// GetSiteInfo provides public site configuration details to the frontend.
// @Summary Get Site Configuration
// @Description Retrieves public site configuration details like email policies and registration status.
// @Tags Site
// @Produce json
// @Success 200 {object} SiteInfoResponse "Successfully retrieved site information"
// @Router /api/v1/site/info [get]
func (h *SiteHandler) GetSiteInfo(c *gin.Context) {
	h.logger.InfoContext(c.Request.Context(), "Fetching site info")
	response := SiteInfoResponse{
		EnableEmailFeatures: h.appConfig.Auth.EnableEmailFeatures,
		EnsureEmail:         h.appConfig.Auth.EnsureEmail,
		AllowRegistration:   h.appConfig.Auth.AllowRegistration,
	}
	common.SendSuccessResponse(c, http.StatusOK, response)
}
