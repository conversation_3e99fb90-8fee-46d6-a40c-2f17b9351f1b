package encoding

import (
	"bytes"
	"io"
	"unicode/utf8"

	"github.com/Sakura-Byte/kikoeru-go/pkg/log"
	"golang.org/x/text/encoding/japanese"
	"golang.org/x/text/encoding/simplifiedchinese"
	"golang.org/x/text/encoding/traditionalchinese"
	"golang.org/x/text/transform"
)

// Common encodings
const (
	EncodingUnknown = iota
	EncodingUTF8
	EncodingShiftJIS
	EncodingEUCJP
	EncodingGB18030
	EncodingBig5
)

// DecodeName attempts to decode a filename from various encodings
// It tries several common encodings used in archives from Japan and China
func DecodeName(originalBytesAsString string) string {
	// If the input string is already valid UTF-8, return it as is.
	// This handles cases where the filename was correctly flagged as UTF-8,
	// or if it's plain ASCII (which is valid UTF-8).
	log.Debug("[encoding.DecodeName] Received input", "input", originalBytesAsString, "hex", []byte(originalBytesAsString))
	if utf8.ValidString(originalBytesAsString) {
		log.Debug("[encoding.DecodeName] Input is valid UTF-8. Returning as is", "input", originalBytesAsString)
		return originalBytesAsString
	}

	// Attempt to decode from Shift-JIS.
	// If successful and the result is valid UTF-8, return it.
	if decoded, err := decodeShiftJIS(originalBytesAsString); err == nil && utf8.ValidString(decoded) {
		log.Debug("[encoding.DecodeName] Decoded successfully as Shift-JIS", "result", decoded, "hex", []byte(decoded))
		return decoded
	}

	// Attempt to decode from EUC-JP.
	if decoded, err := decodeEUCJP(originalBytesAsString); err == nil && utf8.ValidString(decoded) {
		log.Debug("[encoding.DecodeName] Decoded successfully as EUC-JP", "result", decoded, "hex", []byte(decoded))
		return decoded
	}

	// Attempt to decode from GB18030 (Simplified Chinese).
	if decoded, err := decodeGB18030(originalBytesAsString); err == nil && utf8.ValidString(decoded) {
		log.Debug("[encoding.DecodeName] Decoded successfully as GB18030", "result", decoded, "hex", []byte(decoded))
		return decoded
	}

	// Attempt to decode from Big5 (Traditional Chinese).
	if decoded, err := decodeBig5(originalBytesAsString); err == nil && utf8.ValidString(decoded) {
		log.Debug("[encoding.DecodeName] Decoded successfully as Big5", "result", decoded, "hex", []byte(decoded))
		return decoded
	}

	// Fallback: If all decoding attempts fail, log a warning and return the original string.
	// This might still be mojibake but is better than losing the name or returning an error string.
	log.Warn("Could not decode filename from any known encoding. Returning original (potentially garbled) data",
		"original", originalBytesAsString, "hex", []byte(originalBytesAsString))
	return originalBytesAsString
}

// DetectEncoding tries to determine the encoding of a string
func DetectEncoding(s string) int {
	if isASCII(s) {
		return EncodingUTF8
	}

	if isValidUTF8(s) {
		return EncodingUTF8
	}

	// Try decoding with each encoding and check for errors
	if _, err := decodeShiftJIS(s); err == nil {
		return EncodingShiftJIS
	}

	if _, err := decodeEUCJP(s); err == nil {
		return EncodingEUCJP
	}

	if _, err := decodeGB18030(s); err == nil {
		return EncodingGB18030
	}

	if _, err := decodeBig5(s); err == nil {
		return EncodingBig5
	}

	return EncodingUnknown
}

// Helper functions for each encoding

func decodeShiftJIS(s string) (string, error) {
	decoded, err := io.ReadAll(transform.NewReader(bytes.NewReader([]byte(s)), japanese.ShiftJIS.NewDecoder()))
	if err != nil {
		return "", err
	}
	return string(decoded), nil
}

func decodeEUCJP(s string) (string, error) {
	decoded, err := io.ReadAll(transform.NewReader(bytes.NewReader([]byte(s)), japanese.EUCJP.NewDecoder()))
	if err != nil {
		return "", err
	}
	return string(decoded), nil
}

func decodeGB18030(s string) (string, error) {
	decoded, err := io.ReadAll(transform.NewReader(bytes.NewReader([]byte(s)), simplifiedchinese.GB18030.NewDecoder()))
	if err != nil {
		return "", err
	}
	return string(decoded), nil
}

func decodeBig5(s string) (string, error) {
	decoded, err := io.ReadAll(transform.NewReader(bytes.NewReader([]byte(s)), traditionalchinese.Big5.NewDecoder()))
	if err != nil {
		return "", err
	}
	return string(decoded), nil
}

// isASCII checks if a string contains only ASCII characters
func isASCII(s string) bool {
	for i := 0; i < len(s); i++ {
		if s[i] > 127 {
			return false
		}
	}
	return true
}

// isValidUTF8 checks if a string is valid UTF-8
func isValidUTF8(s string) bool {
	return utf8.ValidString(s)
}
