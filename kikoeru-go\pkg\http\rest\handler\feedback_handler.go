package handler

import (
	"errors"
	"fmt"
	"log/slog"
	"net/http"
	"strconv"

	// Added for parsing Subject/Message from Content
	"github.com/Sakura-Byte/kikoeru-go/pkg/apperrors"
	"github.com/Sakura-Byte/kikoeru-go/pkg/models"

	// "github.com/Sakura-Byte/kikoeru-go/pkg/auth" // Removed unused import
	feedback_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/feedback"
	"github.com/Sakura-Byte/kikoeru-go/pkg/http/middleware"
	"github.com/Sakura-Byte/kikoeru-go/pkg/http/rest/common"

	"github.com/Sakura-Byte/kikoeru-go/pkg/ports"
	"github.com/Sakura-Byte/kikoeru-go/pkg/storage/database"
	"github.com/gin-gonic/gin"
)

type FeedbackHandler struct {
	feedbackService ports.FeedbackService
	logger          *slog.Logger
}

func NewFeedbackHandler(feedbackService ports.FeedbackService) *FeedbackHandler {
	baseLogger := slog.Default()
	return &FeedbackHandler{
		feedbackService: feedbackService,
		logger:          baseLogger.With("handler", "FeedbackHandler"),
	}
}

// mapFeedbackToDTO converts a models.Feedback to a feedback_dto.FeedbackResponse
func mapFeedbackToDTO(fb *models.Feedback) feedback_dto.FeedbackResponse {
	dto := feedback_dto.FeedbackResponse{
		ID:        fb.ID,
		Category:  fb.Category,
		Content:   fb.Content, // Use Content directly instead of parsing Subject/Message
		Status:    fb.Status,
		CreatedAt: fb.CreatedAt,
		UpdatedAt: fb.UpdatedAt,
	}

	// Handle User field instead of SubmittedByUserID
	if fb.User != nil {
		userResp := fb.User.ToUserResponse()
		dto.User = &userResp
	}

	if fb.ResolutionNotes.Valid {
		dto.ResolutionNotes = &fb.ResolutionNotes.String
	}

	if fb.ResolvedBy.Valid {
		dto.ResolvedBy = &fb.ResolvedBy.String
	}

	if fb.ResolvedAt.Valid {
		dto.ResolvedAt = &fb.ResolvedAt.Time
	}

	return dto
}

// SubmitFeedback godoc
// @Summary Submit new feedback
// @Description Allows any user (authenticated or anonymous) to submit feedback.
// @Tags feedback
// @Accept json
// @Produce json
// @Param feedback_submission_request body feedback_dto.FeedbackSubmissionRequest true "Feedback submission details"
// @Success 201 {object} feedback_dto.FeedbackResponse "Feedback submitted successfully"
// @Failure 400 {object} common.ErrorResponse "Invalid request payload or validation error"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /feedback [post]
func (h *FeedbackHandler) SubmitFeedback(c *gin.Context) {
	var req feedback_dto.FeedbackSubmissionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WarnContext(c.Request.Context(), "Failed to bind JSON for feedback submission", "error", err)
		common.SendErrorResponse(c, http.StatusBadRequest, fmt.Sprintf("Invalid request payload: %v", err))
		return
	}

	var userID *string
	userClaims := middleware.GetUserClaims(c)
	if userClaims != nil {
		userID = &userClaims.UserID
	}

	feedback, err := h.feedbackService.SubmitFeedback(c.Request.Context(), req, userID, c.ClientIP())
	if err != nil {
		if errors.Is(err, apperrors.ErrValidation) {
			common.SendErrorResponse(c, http.StatusBadRequest, err.Error())
		} else {
			h.logger.ErrorContext(c.Request.Context(), "Failed to submit feedback", "error", err)
			common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to submit feedback: "+err.Error())
		}
		return
	}

	c.JSON(http.StatusCreated, mapFeedbackToDTO(feedback))
}

// ListFeedback godoc
// @Summary List feedback entries (Admin)
// @Description Retrieves a paginated list of feedback entries. Admin access required.
// @Tags feedback
// @Produce json
// @Param page query int false "Page number for pagination" default(1)
// @Param page_size query int false "Number of items per page" default(20)
// @Param sort_by query string false "Field to sort by (e.g., created_at, status)"
// @Param sort_order query string false "Sort order (asc or desc)"
// @Param status query string false "Filter by status (e.g., open, closed, in_progress)"
// @Param type query string false "Filter by type (e.g., bug, feature_request, general)"
// @Param submitted_by_user_id query string false "Filter by submitter's user ID"
// @Success 200 {object} common.PaginatedResponse{data=[]feedback_dto.FeedbackResponse} "List of feedback entries"
// @Failure 400 {object} common.ErrorResponse "Invalid query parameters"
// @Failure 401 {object} common.ErrorResponse "Unauthorized"
// @Failure 403 {object} common.ErrorResponse "Forbidden"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /admin/feedback [get]
// @Security BearerAuth
func (h *FeedbackHandler) ListFeedback(c *gin.Context) {
	var params database.ListFeedbackParams
	if err := c.ShouldBindQuery(&params); err != nil {
		common.SendErrorResponse(c, http.StatusBadRequest, fmt.Sprintf("Invalid query parameters: %v", err))
		return
	}

	userClaims := middleware.GetUserClaims(c)
	if userClaims == nil {
		common.SendErrorResponse(c, http.StatusUnauthorized, "Authentication required")
		return
	}

	feedbackEntries, totalCount, err := h.feedbackService.ListFeedback(c.Request.Context(), params, userClaims)
	if err != nil {
		h.logger.ErrorContext(c.Request.Context(), "Failed to list feedback", "params", params, "error", err)
		common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to retrieve feedback list: "+err.Error())
		return
	}

	feedbackDTOs := make([]feedback_dto.FeedbackResponse, len(feedbackEntries))
	for i, fb := range feedbackEntries {
		feedbackDTOs[i] = mapFeedbackToDTO(fb)
	}

	common.SendPaginatedResponse(c, http.StatusOK, feedbackDTOs, totalCount, params.Page, params.PageSize)
}

// GetFeedback godoc
// @Summary Get a specific feedback entry (Admin)
// @Description Retrieves details of a specific feedback entry by its ID. Admin access required.
// @Tags feedback
// @Produce json
// @Param feedbackID path uint true "Feedback ID"
// @Success 200 {object} feedback_dto.FeedbackResponse "Feedback details"
// @Failure 400 {object} common.ErrorResponse "Invalid feedback ID"
// @Failure 401 {object} common.ErrorResponse "Unauthorized"
// @Failure 403 {object} common.ErrorResponse "Forbidden"
// @Failure 404 {object} common.ErrorResponse "Feedback not found"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /admin/feedback/{feedbackID} [get]
// @Security BearerAuth
func (h *FeedbackHandler) GetFeedback(c *gin.Context) {
	feedbackIDStr := c.Param("feedbackID")
	feedbackID, err := strconv.ParseUint(feedbackIDStr, 10, 32)
	if err != nil {
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid feedback ID format")
		return
	}
	userClaims := middleware.GetUserClaims(c)
	if userClaims == nil {
		common.SendErrorResponse(c, http.StatusUnauthorized, "Authentication required")
		return
	}

	feedback, err := h.feedbackService.GetFeedbackByID(c.Request.Context(), uint(feedbackID), userClaims)
	if err != nil {
		if errors.Is(err, apperrors.ErrFeedbackNotFound) {
			common.SendErrorResponse(c, http.StatusNotFound, "Feedback not found")
		} else {
			h.logger.ErrorContext(c.Request.Context(), "Failed to get feedback by ID", "feedback_id", feedbackID, "error", err)
			common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to retrieve feedback: "+err.Error())
		}
		return
	}
	c.JSON(http.StatusOK, mapFeedbackToDTO(feedback))
}

// UpdateFeedback godoc
// @Summary Update a feedback entry (Admin)
// @Description Updates the status or admin notes of a specific feedback entry. Admin access required.
// @Tags feedback
// @Accept json
// @Produce json
// @Param feedbackID path uint true "Feedback ID"
// @Param update_feedback_request body feedback_dto.UpdateFeedbackRequest true "Feedback update details"
// @Success 200 {object} feedback_dto.FeedbackResponse "Feedback updated successfully"
// @Failure 400 {object} common.ErrorResponse "Invalid feedback ID or request payload"
// @Failure 401 {object} common.ErrorResponse "Unauthorized"
// @Failure 403 {object} common.ErrorResponse "Forbidden"
// @Failure 404 {object} common.ErrorResponse "Feedback not found"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /admin/feedback/{feedbackID} [put]
// @Security BearerAuth
func (h *FeedbackHandler) UpdateFeedback(c *gin.Context) {
	feedbackIDStr := c.Param("feedbackID")
	feedbackID, err := strconv.ParseUint(feedbackIDStr, 10, 32)
	if err != nil {
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid feedback ID format")
		return
	}

	var req feedback_dto.UpdateFeedbackRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WarnContext(c.Request.Context(), "Failed to bind JSON for update feedback", "feedback_id", feedbackID, "error", err)
		common.SendErrorResponse(c, http.StatusBadRequest, fmt.Sprintf("Invalid request payload: %v", err))
		return
	}

	userClaims := middleware.GetUserClaims(c)
	if userClaims == nil {
		common.SendErrorResponse(c, http.StatusUnauthorized, "Authentication required")
		return
	}

	updatedFeedback, err := h.feedbackService.UpdateFeedback(c.Request.Context(), uint(feedbackID), req, userClaims.UserID)
	if err != nil {
		if errors.Is(err, apperrors.ErrFeedbackNotFound) {
			common.SendErrorResponse(c, http.StatusNotFound, "Feedback not found")
		} else if errors.Is(err, apperrors.ErrValidation) {
			common.SendErrorResponse(c, http.StatusBadRequest, err.Error())
		} else {
			h.logger.ErrorContext(c.Request.Context(), "Failed to update feedback", "feedback_id", feedbackID, "error", err)
			common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to update feedback: "+err.Error())
		}
		return
	}
	c.JSON(http.StatusOK, mapFeedbackToDTO(updatedFeedback))
}
