package service

import (
	"context"
	"errors"
	"fmt"
	"io"
	"log/slog"
	"mime/multipart"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/Sakura-Byte/kikoeru-go/pkg/apperrors"
	"github.com/Sakura-Byte/kikoeru-go/pkg/config"
	"github.com/Sakura-Byte/kikoeru-go/pkg/domain/model"
	"github.com/Sakura-Byte/kikoeru-go/pkg/domain/repository"
	"github.com/Sakura-Byte/kikoeru-go/pkg/log"
	"github.com/Sakura-Byte/kikoeru-go/pkg/ports"
	"github.com/google/uuid"
)

// SubtitleService implements ports.SubtitleService
type SubtitleService struct {
	subtitleRepo repository.SubtitleRepository
	workRepo     repository.WorkRepository
	userRepo     repository.UserRepository
	config       *config.AppConfig
	logger       *slog.Logger
}

// NewSubtitleService creates a new subtitle service
func NewSubtitleService(
	subtitleRepo repository.SubtitleRepository,
	workRepo repository.WorkRepository,
	userRepo repository.UserRepository,
	config *config.AppConfig,
	logger *slog.Logger,
) ports.SubtitleService {
	return &SubtitleService{
		subtitleRepo: subtitleRepo,
		workRepo:     workRepo,
		userRepo:     userRepo,
		config:       config,
		logger:       logger,
	}
}

// UploadSubtitle uploads a subtitle file
func (s *SubtitleService) UploadSubtitle(ctx context.Context, userID string, fileHeader *multipart.FileHeader, req ports.SubtitleUploadRequest) (*ports.SubtitleInfo, error) {
	// Check if user is guest
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	// Assuming "guest" is the group name for guest users
	if user.Group == "guest" {
		return nil, apperrors.ErrGuestActionNotAllowed
	}

	// Validate the format
	format := strings.ToLower(req.Format)
	if !isValidSubtitleFormat(format) {
		return nil, apperrors.ErrInvalidSubtitleFormat
	}

	// Check file size
	if fileHeader.Size > 5*1024*1024 { // 5MB limit
		return nil, apperrors.ErrSubtitleTooLarge
	}

	// Lookup work by original ID
	work, err := s.workRepo.FindWorkByOriginalID(ctx, req.OriginalID)
	if err != nil {
		return nil, fmt.Errorf("failed to find work: %w", err)
	}

	// Create a unique UUID for the subtitle file
	fileUUID := uuid.New().String()

	// Get lyrics directory path
	lyricsDir := filepath.Join(s.config.Paths.DataDir, s.config.Paths.LyricsDir)
	if err := os.MkdirAll(lyricsDir, 0750); err != nil {
		return nil, fmt.Errorf("failed to create lyrics directory: %w", err)
	}

	// Create the subtitle file path
	subtitleFilename := fmt.Sprintf("%s.%s", fileUUID, format)
	subtitlePath := filepath.Join(lyricsDir, subtitleFilename)

	// Open and save the file
	srcFile, err := fileHeader.Open()
	if err != nil {
		return nil, fmt.Errorf("failed to open uploaded file: %w", err)
	}
	defer srcFile.Close()

	dstFile, err := os.Create(subtitlePath)
	if err != nil {
		return nil, fmt.Errorf("failed to create subtitle file: %w", err)
	}
	defer dstFile.Close()

	if _, err = io.Copy(dstFile, srcFile); err != nil {
		return nil, fmt.Errorf("failed to save subtitle file: %w", err)
	}

	// Handle multiple tracks
	if len(req.TrackPaths) == 0 {
		return nil, errors.New("at least one track path is required")
	}
	// We'll only use track paths in the join table
	s.logger.Info("Processing subtitle upload", "tracks_count", len(req.TrackPaths))

	// Create the subtitle model
	subtitle := &model.Subtitle{
		UUID:         uuid.New().String(),
		OriginalID:   req.OriginalID,
		WorkID:       work.ID,
		Format:       req.Format,
		UploaderID:   userID,
		SubtitleType: "user_submitted",
		Description:  req.Description,
		IsPublic:     req.IsPublic,
		UpVotes:      0,
		DownVotes:    0,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	// Save the subtitle to the database
	if err := s.subtitleRepo.CreateSubtitle(ctx, subtitle); err != nil {
		// Clean up the file if database insertion fails
		os.Remove(subtitlePath)
		return nil, fmt.Errorf("failed to save subtitle to database: %w", err)
	}

	// Create track associations in the join table
	for _, trackPath := range req.TrackPaths {
		// Parse track path to check if it's in an archive
		isInArchive := false
		archivePath := ""

		// If the path contains a marker for being in an archive, parse it
		// This is a simplified example - adjust according to your actual path format
		if strings.Contains(trackPath, "::archive::") {
			parts := strings.Split(trackPath, "::archive::")
			if len(parts) >= 2 {
				trackPath = parts[0]
				archivePath = parts[1]
				isInArchive = true
			}
		}

		// Create track association
		err := s.subtitleRepo.AddTrackToSubtitle(ctx, subtitle.ID, trackPath, isInArchive, archivePath)
		if err != nil {
			s.logger.Warn("Failed to add track to subtitle", "error", err, "subtitle_id", subtitle.ID, "track_path", trackPath)
			// Continue with other tracks even if one fails
		}
	}

	// Convert to SubtitleInfo response
	return s.subtitleToInfo(ctx, subtitle, userID)
}

// GetSubtitleContent retrieves the content of a subtitle
func (s *SubtitleService) GetSubtitleContent(ctx context.Context, subtitleID uint) (io.ReadCloser, string, string, error) {
	// Get subtitle from database
	subtitle, err := s.subtitleRepo.GetSubtitleByID(ctx, subtitleID)
	if err != nil {
		return nil, "", "", err
	}

	// Determine file path
	lyricsDir := filepath.Join(s.config.Paths.DataDir, s.config.Paths.LyricsDir)
	filename := fmt.Sprintf("%s.%s", subtitle.UUID, subtitle.Format)
	filePath := filepath.Join(lyricsDir, filename)

	// Open the file
	file, err := os.Open(filePath)
	if err != nil {
		if os.IsNotExist(err) {
			return nil, "", "", apperrors.ErrFileNotFound
		}
		return nil, "", "", fmt.Errorf("failed to open subtitle file: %w", err)
	}

	// Determine content type based on format
	contentType := getContentType(subtitle.Format)

	// Return the file reader, content type, and filename
	return file, contentType, filename, nil
}

// FindSubtitlesForTrack finds all subtitles for a specific track
func (s *SubtitleService) FindSubtitlesForTrack(ctx context.Context, req ports.SubtitleQueryRequest, userID string) ([]ports.SubtitleInfo, error) {
	// Find subtitles in database
	subtitles, err := s.subtitleRepo.FindSubtitlesForTrack(ctx, req.OriginalID, req.TrackPath, req.IsInArchive, req.ArchivePath)
	if err != nil {
		return nil, fmt.Errorf("failed to find subtitles: %w", err)
	}

	// Convert to SubtitleInfo response with user votes
	var result []ports.SubtitleInfo
	for _, subtitle := range subtitles {
		// Skip non-public subtitles for guest users
		if req.IsGuest && !subtitle.IsPublic {
			continue
		}

		info, err := s.subtitleToInfo(ctx, &subtitle, userID)
		if err != nil {
			continue // Skip this subtitle if there's an error
		}
		result = append(result, *info)
	}

	return result, nil
}

// VoteOnSubtitle records a user's vote on a subtitle
func (s *SubtitleService) VoteOnSubtitle(ctx context.Context, userID string, req ports.SubtitleVoteRequest) error {
	// Validate vote value
	if req.Vote != 1 && req.Vote != -1 {
		return apperrors.ErrInvalidVoteValue
	}

	// Get subtitle from database
	subtitle, err := s.subtitleRepo.GetSubtitleByID(ctx, req.SubtitleID)
	if err != nil {
		return err
	}

	// Get existing vote if any
	existingVote, err := s.subtitleRepo.GetUserVote(ctx, req.SubtitleID, userID)
	if err != nil {
		return fmt.Errorf("failed to check existing vote: %w", err)
	}

	// Update vote counts based on previous and new votes
	if existingVote != nil {
		if existingVote.Vote == req.Vote {
			// Same vote, no change needed
			return nil
		}

		// Reverse previous vote
		if existingVote.Vote == 1 {
			subtitle.UpVotes--
		} else {
			subtitle.DownVotes--
		}
	}

	// Apply new vote
	if req.Vote == 1 {
		subtitle.UpVotes++
	} else {
		subtitle.DownVotes++
	}

	// Save updated subtitle
	if err := s.subtitleRepo.UpdateSubtitle(ctx, subtitle); err != nil {
		return fmt.Errorf("failed to update subtitle vote counts: %w", err)
	}

	// Save or update vote record
	vote := &model.SubtitleVote{
		SubtitleID: req.SubtitleID,
		UserID:     userID,
		Vote:       req.Vote,
	}

	return s.subtitleRepo.SaveVote(ctx, vote)
}

// DeleteSubtitle deletes a subtitle
func (s *SubtitleService) DeleteSubtitle(ctx context.Context, userID string, subtitleID uint, isAdmin bool) error {
	// Get subtitle from database
	subtitle, err := s.subtitleRepo.GetSubtitleByID(ctx, subtitleID)
	if err != nil {
		return err
	}

	// Check if user is the owner or admin
	if !isAdmin && subtitle.UploaderID != userID {
		return fmt.Errorf("user is not authorized to delete this subtitle")
	}

	// Delete the subtitle from database
	if err := s.subtitleRepo.DeleteSubtitle(ctx, subtitleID); err != nil {
		return fmt.Errorf("failed to delete subtitle from database: %w", err)
	}

	// Delete the file
	lyricsDir := filepath.Join(s.config.Paths.DataDir, s.config.Paths.LyricsDir)
	filename := fmt.Sprintf("%s.%s", subtitle.UUID, subtitle.Format)
	filePath := filepath.Join(lyricsDir, filename)

	if err := os.Remove(filePath); err != nil {
		if !os.IsNotExist(err) {
			// Log but don't fail if file removal fails
			log.Warn("Failed to delete subtitle file", "error", err)
		}
	}

	return nil
}

// UpdateSubtitleVisibility updates the visibility of a subtitle (admin only)
func (s *SubtitleService) UpdateSubtitleVisibility(ctx context.Context, subtitleID uint, isPublic bool) error {
	// Get subtitle from database
	subtitle, err := s.subtitleRepo.GetSubtitleByID(ctx, subtitleID)
	if err != nil {
		return err
	}

	// Update visibility
	subtitle.IsPublic = isPublic

	// Save to database
	return s.subtitleRepo.UpdateSubtitle(ctx, subtitle)
}

// GetAllSubtitles retrieves all subtitles with pagination (admin only)
func (s *SubtitleService) GetAllSubtitles(ctx context.Context, page, pageSize int) ([]ports.SubtitleInfo, int, error) {
	// Get subtitles from database
	subtitles, total, err := s.subtitleRepo.GetAllSubtitles(ctx, page, pageSize)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get subtitles: %w", err)
	}

	// Convert to SubtitleInfo response
	var result []ports.SubtitleInfo
	for _, subtitle := range subtitles {
		info, err := s.subtitleToInfo(ctx, &subtitle, "")
		if err != nil {
			continue // Skip this subtitle if there's an error
		}
		result = append(result, *info)
	}

	return result, total, nil
}

// Helper function to convert a Subtitle model to SubtitleInfo
func (s *SubtitleService) subtitleToInfo(ctx context.Context, subtitle *model.Subtitle, userID string) (*ports.SubtitleInfo, error) {
	info := &ports.SubtitleInfo{
		ID:           subtitle.ID,
		UUID:         subtitle.UUID,
		OriginalID:   subtitle.OriginalID,
		WorkID:       subtitle.WorkID,
		Format:       subtitle.Format,
		UploaderID:   subtitle.UploaderID,
		SubtitleType: subtitle.SubtitleType,
		Description:  subtitle.Description,
		IsPublic:     subtitle.IsPublic,
		UpVotes:      subtitle.UpVotes,
		DownVotes:    subtitle.DownVotes,
		CreatedAt:    subtitle.CreatedAt,
		UpdatedAt:    subtitle.UpdatedAt,
	}

	// Get uploader username if possible
	if user, err := s.userRepo.GetByID(ctx, subtitle.UploaderID); err == nil {
		info.UploaderName = user.Username
	}

	// Get user's vote if a user ID is provided
	if userID != "" {
		if vote, err := s.subtitleRepo.GetUserVote(ctx, subtitle.ID, userID); err == nil && vote != nil {
			info.CurrentUserVote = vote.Vote
		}
	}

	// Get the track paths from the join table
	trackPaths, err := s.getTrackPathsForSubtitle(ctx, subtitle.ID)
	if err != nil {
		return info, nil // Continue even if we can't get track paths
	}
	info.TrackPaths = trackPaths

	return info, nil
}

// Helper function to check if a subtitle format is valid
func isValidSubtitleFormat(format string) bool {
	validFormats := map[string]bool{
		"srt": true,
		"vtt": true,
		"ass": true,
		"ssa": true,
		"lrc": true,
	}
	return validFormats[format]
}

// Helper function to get content type based on format
func getContentType(format string) string {
	contentTypes := map[string]string{
		"srt": "application/x-subrip",
		"vtt": "text/vtt",
		"ass": "text/plain",
		"ssa": "text/plain",
		"lrc": "text/plain",
	}

	if ct, ok := contentTypes[format]; ok {
		return ct
	}
	return "text/plain"
}

// getTrackPathsForSubtitle fetches all track paths associated with a subtitle
func (s *SubtitleService) getTrackPathsForSubtitle(ctx context.Context, subtitleID uint) ([]string, error) {
	tracks, err := s.subtitleRepo.GetTracksForSubtitle(ctx, subtitleID)
	if err != nil {
		return nil, err
	}

	trackPaths := make([]string, len(tracks))
	for i, track := range tracks {
		trackPaths[i] = track.TrackPath
	}

	return trackPaths, nil
}
