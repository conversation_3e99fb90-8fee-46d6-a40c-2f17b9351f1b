package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log/slog"
	"math/rand"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/Sakura-Byte/kikoeru-go/pkg/apperrors"
	"github.com/Sakura-Byte/kikoeru-go/pkg/auth"
	"github.com/Sakura-Byte/kikoeru-go/pkg/config"
	circle_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/circle"
	common_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/common"
	scraper_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/scraper"
	tag_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/tag"
	va_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/va"
	work_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/work"
	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
	"github.com/Sakura-Byte/kikoeru-go/pkg/ports"
	"github.com/Sakura-Byte/kikoeru-go/pkg/storage/database"
)

var rjCodePattern = regexp.MustCompile(`(?i)(RJ|VJ|BJ)(\d{6,8})`)

// GenerateWorkCoverURL constructs the API path for a work's cover image.
func GenerateWorkCoverURL(workID uint) string {
	return fmt.Sprintf("/api/v1/media/cover/work/%d", workID)
}

// CoverType and its constants have been moved to github.com/Sakura-Byte/kikoeru-go/pkg/ports

// MediaService interface has been moved to github.com/Sakura-Byte/kikoeru-go/pkg/ports

// --- DTOs ---
// WorkDTO (formerly WorkInfoResponse) DTO has been moved to pkg/dto/work
// RandomQueueParams DTO has been moved to pkg/dto/work
// UpdateWorkRequest DTO has been moved to pkg/dto/work

// --- Service Interface and Implementation ---
// WorkService interface now implicitly includes ports.WorkScrapeTriggerService
// by having matching methods.
// WorkService interface has been moved to github.com/Sakura-Byte/kikoeru-go/pkg/ports

type workService struct {
	workRepo     database.WorkRepository
	tagRepo      database.TagRepository
	vaRepo       database.VARepository
	circleRepo   database.CircleRepository
	playlistRepo database.PlaylistRepository
	// favRepo      database.FavoriteRepository // Removed
	reviewRepo database.ReviewRepository
	// reviewService  ReviewService // Removed: Review feature disabled
	scraperService ports.ScraperService
	mediaService   ports.MediaService
	logger         *slog.Logger
	cfg            *config.AppConfig
}

// Ensure workService implements ports.WorkService
var _ ports.WorkService = (*workService)(nil)

func NewWorkService(
	workRepo database.WorkRepository,
	tagRepo database.TagRepository,
	vaRepo database.VARepository,
	circleRepo database.CircleRepository,
	playlistRepo database.PlaylistRepository,
	// favRepo database.FavoriteRepository, // Removed
	reviewRepo database.ReviewRepository,
	// reviewService ReviewService, // Removed: Review feature disabled
	scraperService ports.ScraperService,
	mediaService ports.MediaService,
	cfg *config.AppConfig,
	logger *slog.Logger,
) ports.WorkService {
	return &workService{
		workRepo:     workRepo,
		tagRepo:      tagRepo,
		vaRepo:       vaRepo,
		circleRepo:   circleRepo,
		playlistRepo: playlistRepo,
		// favRepo:      favRepo, // Removed
		reviewRepo: reviewRepo,
		// reviewService:  reviewService, // Removed: Review feature disabled
		scraperService: scraperService,
		mediaService:   mediaService,
		cfg:            cfg,
		logger:         logger.With("service", "WorkService"),
	}
}

// mapWorkModelToDTO converts a models.Work to a work_dto.WorkDTO, including cover URLs.
func (s *workService) mapWorkModelToDTO(ctx context.Context, workModel *models.Work) *work_dto.WorkDTO {
	if workModel == nil {
		return nil
	}
	dto := &work_dto.WorkDTO{
		ID:              workModel.ID,
		CreatedAt:       workModel.CreatedAt,
		UpdatedAt:       workModel.UpdatedAt,
		OriginalID:      workModel.OriginalID,
		WorkType:        workModel.WorkType,
		Title:           workModel.Title,
		AgeRating:       workModel.AgeRating,
		NSFW:            workModel.NSFW,
		ReleaseDate:     workModel.ReleaseDate,
		RateCount:       workModel.RateCount,
		RateAverage2DP:  workModel.RateAverage2DP,
		Rank:            workModel.Rank,
		LyricStatus:     workModel.LyricStatus,
		Language:        workModel.Language,
		Duration:        workModel.Duration,
		DlCount:         workModel.DlCount,
		Price:           workModel.Price,
		ReviewCount:     workModel.ReviewCount,
		RateCountDetail: workModel.RateCountDetail,
		StorageID:       workModel.StorageID,
		PathInStorage:   workModel.PathInStorage,
		Circle:          circle_dto.MapCircleModelToDTO(workModel.Circle), // Changed to use converter
		Tags:            tag_dto.MapTagModelsToDTOs(workModel.Tags),       // Changed to use converter
		VAs:             va_dto.MapVAModelsToDTOs(workModel.VAs),          // Changed to use converter
		UserRating:      nil,                                              // 用户评分初始为nil
	}

	if workModel.OriginalID != nil && *workModel.OriginalID != "" {
		originalIDStr := *workModel.OriginalID
		baseAPIPath := fmt.Sprintf("/api/v1/cover/%s", originalIDStr)

		mainURL := fmt.Sprintf("%s?type=main", baseAPIPath)
		samURL := fmt.Sprintf("%s?type=sam", baseAPIPath)
		dto.ImageMainURL = &mainURL
		dto.ImageSamURL = &samURL
	}
	return dto
}

func (s *workService) GetWorkByID(ctx context.Context, id uint) (*work_dto.WorkDTO, error) {
	workModel, err := s.workRepo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, apperrors.ErrWorkNotFound) {
			return nil, apperrors.ErrWorkNotFound
		}
		s.logger.ErrorContext(ctx, "Failed to get work by ID from repository", "work_id", id, "error", err)
		return nil, fmt.Errorf("failed to retrieve work: %w", err)
	}
	// s.populateReviewAndFavoriteInfo(ctx, workModel) // This is a placeholder, DTO conversion handles necessary info
	return s.mapWorkModelToDTO(ctx, workModel), nil
}

func (s *workService) GetWorkByOriginalID(ctx context.Context, originalID string) (*work_dto.WorkDTO, error) {
	workModel, err := s.workRepo.GetByOriginalID(ctx, originalID)
	if err != nil {
		if errors.Is(err, apperrors.ErrWorkNotFound) {
			return nil, apperrors.ErrWorkNotFound
		}
		s.logger.ErrorContext(ctx, "Failed to get work by OriginalID from repository", "original_id", originalID, "error", err)
		return nil, fmt.Errorf("failed to retrieve work by originalID: %w", err)
	}
	// s.populateReviewAndFavoriteInfo(ctx, workModel) // This is a placeholder
	return s.mapWorkModelToDTO(ctx, workModel), nil
}

var advancedSearchTypePattern = regexp.MustCompile(`\$type:([a-zA-Z0-9_]+)`)

// 添加一个更通用的正则表达式，匹配所有高级搜索模式
var advancedSearchPattern = regexp.MustCompile(`\$([-]?[a-zA-Z0-9_]+):([^$]+)\$`)

func (s *workService) parseAdvancedQueryAndUpdateParams(ctx context.Context, params *database.ListWorksParams) {
	if params.AdvancedSearchQuery == "" {
		return
	}
	s.logger.DebugContext(ctx, "Parsing advanced search query", "query", params.AdvancedSearchQuery)

	// 处理所有新格式的高级搜索查询
	matches := advancedSearchPattern.FindAllStringSubmatch(params.AdvancedSearchQuery, -1)
	for _, match := range matches {
		if len(match) < 3 {
			continue
		}

		namespace := match[1]
		value := match[2]

		s.logger.DebugContext(ctx, "Processing advanced search parameter", "namespace", namespace, "value", value)

		// 根据命名空间处理不同类型的高级搜索
		switch namespace {
		case "type":
			// 类型搜索 (已在上面处理，为了完整性加入)
			workTypeFromQuery := strings.ToUpper(value)
			if workTypeFromQuery == "RJ" || workTypeFromQuery == "VJ" || workTypeFromQuery == "BJ" {
				params.WorkType = workTypeFromQuery
			} else {
				s.logger.WarnContext(ctx, "Invalid value for $type in advanced query, ignoring.", "value", value)
			}

		case "tag":
			// 标签搜索 - 需要转换为 ID
			tag, err := s.tagRepo.GetByName(ctx, value)
			if err == nil && tag != nil {
				params.IncludeTagIDs = append(params.IncludeTagIDs, tag.ID)
			} else if !errors.Is(err, apperrors.ErrTagNotFound) {
				s.logger.WarnContext(ctx, "Error finding tag for advanced query", "tag_name", value, "error", err)
			}
		case "-tag":
			// 排除标签 - 需要转换为 ID
			tag, err := s.tagRepo.GetByName(ctx, value)
			if err == nil && tag != nil {
				params.ExcludeTagIDs = append(params.ExcludeTagIDs, tag.ID)
			} else if !errors.Is(err, apperrors.ErrTagNotFound) {
				s.logger.WarnContext(ctx, "Error finding tag for exclusion in advanced query", "tag_name", value, "error", err)
			}

		case "circle":
			// 社团搜索 - 需要转换为 ID
			circle, err := s.circleRepo.GetByName(ctx, value)
			if err == nil && circle != nil {
				params.IncludeCircleIDs = append(params.IncludeCircleIDs, circle.ID)
			} else if !errors.Is(err, apperrors.ErrCircleNotFound) {
				s.logger.WarnContext(ctx, "Error finding circle for advanced query", "circle_name", value, "error", err)
			}
		case "-circle":
			// 排除社团 - 需要转换为 ID
			circle, err := s.circleRepo.GetByName(ctx, value)
			if err == nil && circle != nil {
				params.ExcludeCircleIDs = append(params.ExcludeCircleIDs, circle.ID)
			} else if !errors.Is(err, apperrors.ErrCircleNotFound) {
				s.logger.WarnContext(ctx, "Error finding circle for exclusion in advanced query", "circle_name", value, "error", err)
			}

		case "va":
			// 声优搜索 - 需要转换为 ID
			va, err := s.vaRepo.GetByName(ctx, value)
			if err == nil && va != nil {
				params.IncludeVAIDs = append(params.IncludeVAIDs, va.ID)
			} else if !errors.Is(err, apperrors.ErrVANotFound) {
				s.logger.WarnContext(ctx, "Error finding VA for advanced query", "va_name", value, "error", err)
			}
		case "-va":
			// 排除声优 - 需要转换为 ID
			va, err := s.vaRepo.GetByName(ctx, value)
			if err == nil && va != nil {
				params.ExcludeVAIDs = append(params.ExcludeVAIDs, va.ID)
			} else if !errors.Is(err, apperrors.ErrVANotFound) {
				s.logger.WarnContext(ctx, "Error finding VA for exclusion in advanced query", "va_name", value, "error", err)
			}

		case "rate":
			// 筛选评分（大于）
			if rating, err := strconv.ParseFloat(value, 64); err == nil {
				params.MinRateAverage2DP = &rating
			} else {
				s.logger.WarnContext(ctx, "Invalid value for $rate in advanced query, ignoring.", "value", value, "error", err)
			}

		case "price":
			// 筛选价格（大于）
			if price, err := strconv.ParseInt(value, 10, 64); err == nil {
				params.MinPrice = &price
			} else {
				s.logger.WarnContext(ctx, "Invalid value for $price in advanced query, ignoring.", "value", value, "error", err)
			}

		case "sell":
			// 筛选销量（大于）
			if sell, err := strconv.ParseInt(value, 10, 64); err == nil {
				params.MinSell = &sell
			} else {
				s.logger.WarnContext(ctx, "Invalid value for $sell in advanced query, ignoring.", "value", value, "error", err)
			}

		case "age":
			// 筛选年龄分级
			params.AgeRating = value
		case "-age":
			// 排除年龄分级
			params.ExcludeAgeRating = value

		case "duration":
			// 筛选作品时长（大于）
			if duration, err := strconv.ParseInt(value, 10, 64); err == nil {
				params.MinDuration = &duration
			} else {
				s.logger.WarnContext(ctx, "Invalid value for $duration in advanced query, ignoring.", "value", value, "error", err)
			}
		case "-duration":
			// 筛选作品时长（小于）
			if duration, err := strconv.ParseInt(value, 10, 64); err == nil {
				params.MaxDuration = &duration
			} else {
				s.logger.WarnContext(ctx, "Invalid value for $-duration in advanced query, ignoring.", "value", value, "error", err)
			}

		case "lang":
			// 筛选语言
			params.Language = value
		case "-lang":
			// 排除语言
			params.ExcludeLanguage = value

		default:
			s.logger.WarnContext(ctx, "Unknown namespace in advanced query, ignoring.", "namespace", namespace, "value", value)
		}
	}

	// 处理纯文本搜索 (使用AND逻辑)
	// 移除所有高级搜索模式 $namespace:value$
	plainText := advancedSearchPattern.ReplaceAllString(params.AdvancedSearchQuery, "")
	plainText = advancedSearchTypePattern.ReplaceAllString(plainText, "")
	plainText = strings.TrimSpace(plainText)

	if plainText != "" {
		// 将纯文本搜索词拆分为单独的关键词
		terms := strings.Fields(plainText)
		s.logger.DebugContext(ctx, "Extracted plain text search terms", "terms", terms)

		// 将这些关键词添加到TitleTerms中
		if len(terms) > 0 {
			params.PlainTextTerms = terms
		}
	}
}

func (s *workService) ListWorks(ctx context.Context, params database.ListWorksParams) ([]*work_dto.WorkDTO, int64, error) {
	// Convert name-based filters to ID-based filters
	if err := s.convertNamesToIDs(ctx, &params); err != nil {
		s.logger.WarnContext(ctx, "Failed to convert some names to IDs", "error", err)
		// Continue processing even if some names couldn't be resolved
	}

	s.parseAdvancedQueryAndUpdateParams(ctx, &params)
	worksFromRepo, total, err := s.workRepo.List(ctx, params)
	if err != nil {
		s.logger.ErrorContext(ctx, "Failed to list works from repository", "params", fmt.Sprintf("%+v", params), "error", err)
		return nil, 0, fmt.Errorf("failed to list works: %w", err)
	}

	responseItems := make([]*work_dto.WorkDTO, len(worksFromRepo))
	for i, workModel := range worksFromRepo {
		item := &work_dto.WorkDTO{
			ID:              workModel.ID,
			CreatedAt:       workModel.CreatedAt,
			UpdatedAt:       workModel.UpdatedAt,
			OriginalID:      workModel.OriginalID,
			WorkType:        workModel.WorkType,
			Title:           workModel.Title,
			AgeRating:       workModel.AgeRating,
			NSFW:            workModel.NSFW,
			ReleaseDate:     workModel.ReleaseDate,
			RateCount:       workModel.RateCount,
			RateAverage2DP:  workModel.RateAverage2DP,
			Rank:            workModel.Rank,
			LyricStatus:     workModel.LyricStatus,
			Language:        workModel.Language,
			Duration:        workModel.Duration,
			DlCount:         workModel.DlCount,
			Price:           workModel.Price,
			ReviewCount:     workModel.ReviewCount,
			RateCountDetail: workModel.RateCountDetail,
			StorageID:       workModel.StorageID,
			PathInStorage:   workModel.PathInStorage,
			Circle:          circle_dto.MapCircleModelToDTO(workModel.Circle), // Changed to use converter
			Tags:            tag_dto.MapTagModelsToDTOs(workModel.Tags),       // Changed to use converter
			VAs:             va_dto.MapVAModelsToDTOs(workModel.VAs),          // Changed to use converter
			UserRating:      nil,                                              // 用户评分初始为nil
		}

		if workModel.OriginalID != nil && *workModel.OriginalID != "" {
			originalIDStr := *workModel.OriginalID
			baseAPIPath := fmt.Sprintf("/api/v1/cover/%s", originalIDStr)

			mainURL := fmt.Sprintf("%s?type=main", baseAPIPath)
			samURL := fmt.Sprintf("%s?type=sam", baseAPIPath)
			item.ImageMainURL = &mainURL
			item.ImageSamURL = &samURL
		} else {
			item.ImageMainURL = nil
			item.ImageSamURL = nil
		}
		responseItems[i] = item
	}
	return responseItems, total, nil
}

func (s *workService) UpdateWorkByAdmin(ctx context.Context, claims *auth.Claims, workID uint, req work_dto.UpdateWorkRequest) (*work_dto.WorkDTO, error) {
	if err := s._ensureAdmin(claims); err != nil {
		return nil, err
	}
	work, err := s.workRepo.GetByID(ctx, workID)
	if err != nil {
		if errors.Is(err, apperrors.ErrWorkNotFound) {
			return nil, apperrors.ErrWorkNotFound
		}
		s.logger.ErrorContext(ctx, "Failed to get work for update", "work_id", workID, "error", err)
		return nil, fmt.Errorf("failed to retrieve work for update: %w", err)
	}
	updated := false
	if req.Title != nil && *req.Title != "" && work.Title != *req.Title {
		work.Title = *req.Title
		updated = true
	}
	if req.OriginalID != nil {
		if *req.OriginalID == "" {
			if work.OriginalID != nil {
				work.OriginalID = nil
				updated = true
			}
		} else if work.OriginalID == nil || *work.OriginalID != *req.OriginalID {
			work.OriginalID = req.OriginalID
			updated = true
		}
	}
	if req.WorkType != nil {
		wt := strings.ToUpper(*req.WorkType)
		if wt == "RJ" || wt == "VJ" || wt == "BJ" || wt == "" {
			if wt == "" {
				if work.WorkType != nil {
					work.WorkType = nil
					updated = true
				}
			} else if work.WorkType == nil || *work.WorkType != wt {
				work.WorkType = &wt
				updated = true
			}
		} else {
			s.logger.WarnContext(ctx, "Invalid WorkType provided for update, ignoring", "work_id", workID, "provided_type", *req.WorkType)
		}
	}

	if req.AgeRating != nil {
		if *req.AgeRating == "" {
			if work.AgeRating != nil {
				work.AgeRating = nil
				updated = true
			}
		} else if work.AgeRating == nil || *work.AgeRating != *req.AgeRating {
			work.AgeRating = req.AgeRating
			updated = true
		}
	}
	if req.ReleaseDate != nil {
		if *req.ReleaseDate == "" {
			if work.ReleaseDate != nil {
				work.ReleaseDate = nil
				updated = true
			}
		} else if work.ReleaseDate == nil || *work.ReleaseDate != *req.ReleaseDate {
			work.ReleaseDate = req.ReleaseDate
			updated = true
		}
	}
	if req.Language != nil {
		if *req.Language == "" {
			if work.Language != nil {
				work.Language = nil
				updated = true
			}
		} else if work.Language == nil || *work.Language != *req.Language {
			work.Language = req.Language
			updated = true
		}
	}
	if req.LyricStatus != nil && work.LyricStatus != *req.LyricStatus {
		work.LyricStatus = *req.LyricStatus
		updated = true
	}

	if req.CircleName != nil {
		if *req.CircleName == "" {
			if work.Circle != nil {
				work.Circle = nil
				updated = true
			}
		} else {
			circle, errCircle := s.circleRepo.GetOrCreateCircle(ctx, *req.CircleName)
			if errCircle != nil {
				return nil, fmt.Errorf("failed to get or create circle '%s': %w", *req.CircleName, errCircle)
			}
			if work.Circle == nil || work.Circle.ID != circle.ID {
				work.Circle = circle
				updated = true
			}
		}
	}
	if req.Tags != nil {
		var tagIDs []string
		for _, tagName := range req.Tags {
			tag, errTag := s.tagRepo.GetOrCreateTag(ctx, tagName)
			if errTag != nil {
				return nil, fmt.Errorf("failed to get or create tag '%s': %w", tagName, errTag)
			}
			tagIDs = append(tagIDs, tag.ID)
		}
		if errReplace := s.workRepo.ReplaceWorkTags(ctx, work.ID, tagIDs); errReplace != nil {
			return nil, fmt.Errorf("failed to replace work tags: %w", errReplace)
		}
		updated = true
	}
	if req.VAs != nil {
		var vaIDs []string
		for _, vaName := range req.VAs {
			va, errVA := s.vaRepo.GetOrCreateVA(ctx, vaName)
			if errVA != nil {
				return nil, fmt.Errorf("failed to get or create VA '%s': %w", vaName, errVA)
			}
			vaIDs = append(vaIDs, va.ID)
		}
		if errReplace := s.workRepo.ReplaceWorkVAs(ctx, work.ID, vaIDs); errReplace != nil {
			return nil, fmt.Errorf("failed to replace work VAs: %w", errReplace)
		}
		updated = true
	}

	if req.AdminCover != nil {
		s.logger.InfoContext(ctx, "AdminCover field was present in the update request but is no longer directly used on the Work model.", "work_id", workID, "admin_cover_value", *req.AdminCover)
	}
	if !updated {
		s.logger.InfoContext(ctx, "No changes detected for work update", "work_id", workID)
		// s.populateReviewAndFavoriteInfo(ctx, work) // Placeholder
		return s.mapWorkModelToDTO(ctx, work), nil
	}
	if err := s.workRepo.Update(ctx, work); err != nil {
		s.logger.ErrorContext(ctx, "Failed to update work in repository", "work_id", workID, "error", err)
		return nil, fmt.Errorf("failed to update work: %w", err)
	}
	s.logger.InfoContext(ctx, "Work updated successfully by admin", "work_id", workID, "admin_user", claims.Username)
	updatedWorkModel, getErr := s.workRepo.GetByID(ctx, workID)
	if getErr != nil {
		s.logger.ErrorContext(ctx, "Failed to retrieve updated work after update", "work_id", workID, "error", getErr)
		return nil, getErr
	}
	// s.populateReviewAndFavoriteInfo(ctx, updatedWorkModel) // Placeholder
	return s.mapWorkModelToDTO(ctx, updatedWorkModel), nil
}

func (s *workService) DeleteWorkByAdmin(ctx context.Context, claims *auth.Claims, workID uint) error {
	if err := s._ensureAdmin(claims); err != nil {
		return err
	}
	_, err := s.workRepo.GetByID(ctx, workID)
	if err != nil {
		if errors.Is(err, apperrors.ErrWorkNotFound) {
			return apperrors.ErrWorkNotFound
		}
		s.logger.ErrorContext(ctx, "Failed to get work for deletion check", "work_id", workID, "error", err)
		return fmt.Errorf("failed to retrieve work for deletion: %w", err)
	}
	if err := s.workRepo.Delete(ctx, workID); err != nil {
		s.logger.ErrorContext(ctx, "Failed to delete work from repository", "work_id", workID, "error", err)
		return fmt.Errorf("failed to delete work: %w", err)
	}
	s.logger.InfoContext(ctx, "Work deleted successfully by admin", "work_id", workID, "admin_user", claims.Username)
	return nil
}

func (s *workService) GetRandomWorkQueue(ctx context.Context, claims *auth.Claims, params work_dto.RandomQueueParams) ([]*work_dto.WorkDTO, error) {
	// Add a safe log that handles nil claims
	username := "anonymous"
	if claims != nil && claims.Username != "" {
		username = claims.Username
	}
	s.logger.InfoContext(ctx, "Generating random work queue", "params", fmt.Sprintf("%+v", params), "user", username)
	if params.MaxQueueSize <= 0 {
		params.MaxQueueSize = 50
	}
	if params.MaxQueueSize > 200 {
		params.MaxQueueSize = 200
	}
	var includeTagIDs, excludeTagIDs, includeCircleIDs, excludeCircleIDs []string
	var includeVAIDs, excludeVAIDs []string
	var err error
	resolveTagIDs := func(names []string) ([]string, error) {
		var ids []string
		for _, name := range names {
			tag, err := s.tagRepo.GetByName(ctx, name)
			if err == nil && tag != nil {
				ids = append(ids, tag.ID)
			} else if !errors.Is(err, apperrors.ErrTagNotFound) {
				return nil, err
			}
		}
		return ids, nil
	}
	resolveVAIDs := func(names []string) ([]string, error) {
		var ids []string
		for _, name := range names {
			va, err := s.vaRepo.GetByName(ctx, name)
			if err == nil && va != nil {
				ids = append(ids, va.ID)
			} else if !errors.Is(err, apperrors.ErrVANotFound) {
				return nil, err
			}
		}
		return ids, nil
	}
	resolveCircleIDs := func(names []string) ([]string, error) {
		var ids []string
		for _, name := range names {
			circle, err := s.circleRepo.GetByName(ctx, name)
			if err == nil && circle != nil {
				ids = append(ids, circle.ID)
			} else if !errors.Is(err, apperrors.ErrCircleNotFound) {
				return nil, err
			}
		}
		return ids, nil
	}
	if len(params.IncludeTagNames) > 0 {
		includeTagIDs, err = resolveTagIDs(params.IncludeTagNames)
		if err != nil {
			return nil, fmt.Errorf("failed to resolve include_tag_names: %w", err)
		}
	}
	if len(params.ExcludeTagNames) > 0 {
		excludeTagIDs, err = resolveTagIDs(params.ExcludeTagNames)
		if err != nil {
			return nil, fmt.Errorf("failed to resolve exclude_tag_names: %w", err)
		}
	}
	if len(params.IncludeVANames) > 0 {
		includeVAIDs, err = resolveVAIDs(params.IncludeVANames)
		if err != nil {
			return nil, fmt.Errorf("failed to resolve include_va_names: %w", err)
		}
	}
	if len(params.ExcludeVANames) > 0 {
		excludeVAIDs, err = resolveVAIDs(params.ExcludeVANames)
		if err != nil {
			return nil, fmt.Errorf("failed to resolve exclude_va_names: %w", err)
		}
	}
	if len(params.IncludeCircleNames) > 0 {
		includeCircleIDs, err = resolveCircleIDs(params.IncludeCircleNames)
		if err != nil {
			return nil, fmt.Errorf("failed to resolve include_circle_names: %w", err)
		}
	}
	if len(params.ExcludeCircleNames) > 0 {
		excludeCircleIDs, err = resolveCircleIDs(params.ExcludeCircleNames)
		if err != nil {
			return nil, fmt.Errorf("failed to resolve exclude_circle_names: %w", err)
		}
	}
	if params.SourcePlaylistID != nil && *params.SourcePlaylistID > 0 {
		if claims == nil || claims.Username == "" {
			return nil, errors.New("user authentication required to fetch from specific playlist")
		}
		s.logger.DebugContext(ctx, "Fetching work IDs from playlist", "playlist_id", *params.SourcePlaylistID, "user", claims.Username)
		playlistItems, err := s.playlistRepo.ListItemsByPlaylistID(ctx, *params.SourcePlaylistID, claims.Username)
		if err != nil {
			return nil, fmt.Errorf("failed to get items for playlist %d: %w", *params.SourcePlaylistID, err)
		}
		if len(playlistItems) == 0 {
			return []*work_dto.WorkDTO{}, nil
		}
		var workIDs []uint
		for _, item := range playlistItems {
			workIDs = append(workIDs, item.WorkID)
		}
		workModels, err := s.workRepo.GetWorksByIDs(ctx, workIDs)
		if err != nil {
			return nil, fmt.Errorf("failed to fetch works from playlist: %w", err)
		}
		var filteredWorkModels []*models.Work
		for _, work := range workModels {
			keep := true
			if params.WorkType != nil && *params.WorkType != "" {
				if work.WorkType == nil || (work.WorkType != nil && !strings.EqualFold(*work.WorkType, *params.WorkType)) {
					keep = false
				}
			}
			if params.MinRating != nil {
				if work.RateAverage2DP == nil || (work.RateAverage2DP != nil && *work.RateAverage2DP < *params.MinRating) {
					keep = false
				}
			}
			if keep {
				// s.populateReviewAndFavoriteInfo(ctx, work) // Placeholder
				filteredWorkModels = append(filteredWorkModels, work)
			}
		}
		r := rand.New(rand.NewSource(time.Now().UnixNano()))
		r.Shuffle(len(filteredWorkModels), func(i, j int) {
			filteredWorkModels[i], filteredWorkModels[j] = filteredWorkModels[j], filteredWorkModels[i]
		})

		finalWorkModels := filteredWorkModels
		if len(filteredWorkModels) > params.MaxQueueSize {
			finalWorkModels = filteredWorkModels[:params.MaxQueueSize]
		}

		dtos := make([]*work_dto.WorkDTO, len(finalWorkModels))
		for i, wm := range finalWorkModels {
			dtos[i] = s.mapWorkModelToDTO(ctx, wm)
		}
		return dtos, nil
	} else {
		workModels, err := s.workRepo.GetRandomWorks(ctx, params.MaxQueueSize, includeTagIDs, excludeTagIDs, includeVAIDs, excludeVAIDs, includeCircleIDs, excludeCircleIDs, params.WorkType, params.MinRating)
		if err != nil {
			s.logger.ErrorContext(ctx, "Failed to get random works from repository", "error", err)
			return nil, fmt.Errorf("failed to fetch random works: %w", err)
		}
		dtos := make([]*work_dto.WorkDTO, len(workModels))
		for i, wm := range workModels {
			// s.populateReviewAndFavoriteInfo(ctx, wm) // Placeholder
			dtos[i] = s.mapWorkModelToDTO(ctx, wm)
		}
		return dtos, nil
	}
}

func (s *workService) _ensureAdmin(claims *auth.Claims) error {
	if claims == nil {
		return apperrors.ErrAdminAccessDenied
	}
	if claims.UserGroup != models.UserGroupAdmin {
		return apperrors.ErrAdminAccessDenied
	}
	return nil
}

// convertNamesToIDs converts name-based filters to ID-based filters
func (s *workService) convertNamesToIDs(ctx context.Context, params *database.ListWorksParams) error {
	var errs []string

	// Convert tag names to IDs
	if len(params.IncludeTagNames) > 0 {
		for _, tagName := range params.IncludeTagNames {
			tag, err := s.tagRepo.GetByName(ctx, tagName)
			if err == nil && tag != nil {
				params.IncludeTagIDs = append(params.IncludeTagIDs, tag.ID)
			} else if !errors.Is(err, apperrors.ErrTagNotFound) {
				errs = append(errs, fmt.Sprintf("tag '%s': %v", tagName, err))
			}
		}
	}

	if len(params.ExcludeTagNames) > 0 {
		for _, tagName := range params.ExcludeTagNames {
			tag, err := s.tagRepo.GetByName(ctx, tagName)
			if err == nil && tag != nil {
				params.ExcludeTagIDs = append(params.ExcludeTagIDs, tag.ID)
			} else if !errors.Is(err, apperrors.ErrTagNotFound) {
				errs = append(errs, fmt.Sprintf("tag '%s': %v", tagName, err))
			}
		}
	}

	// Convert VA names to IDs
	if len(params.IncludeVANames) > 0 {
		for _, vaName := range params.IncludeVANames {
			va, err := s.vaRepo.GetByName(ctx, vaName)
			if err == nil && va != nil {
				params.IncludeVAIDs = append(params.IncludeVAIDs, va.ID)
			} else if !errors.Is(err, apperrors.ErrVANotFound) {
				errs = append(errs, fmt.Sprintf("VA '%s': %v", vaName, err))
			}
		}
	}

	if len(params.ExcludeVANames) > 0 {
		for _, vaName := range params.ExcludeVANames {
			va, err := s.vaRepo.GetByName(ctx, vaName)
			if err == nil && va != nil {
				params.ExcludeVAIDs = append(params.ExcludeVAIDs, va.ID)
			} else if !errors.Is(err, apperrors.ErrVANotFound) {
				errs = append(errs, fmt.Sprintf("VA '%s': %v", vaName, err))
			}
		}
	}

	// Convert circle names to IDs
	if len(params.IncludeCircleNames) > 0 {
		for _, circleName := range params.IncludeCircleNames {
			circle, err := s.circleRepo.GetByName(ctx, circleName)
			if err == nil && circle != nil {
				params.IncludeCircleIDs = append(params.IncludeCircleIDs, circle.ID)
			} else if !errors.Is(err, apperrors.ErrCircleNotFound) {
				errs = append(errs, fmt.Sprintf("circle '%s': %v", circleName, err))
			}
		}
	}

	if len(params.ExcludeCircleNames) > 0 {
		for _, circleName := range params.ExcludeCircleNames {
			circle, err := s.circleRepo.GetByName(ctx, circleName)
			if err == nil && circle != nil {
				params.ExcludeCircleIDs = append(params.ExcludeCircleIDs, circle.ID)
			} else if !errors.Is(err, apperrors.ErrCircleNotFound) {
				errs = append(errs, fmt.Sprintf("circle '%s': %v", circleName, err))
			}
		}
	}

	if len(errs) > 0 {
		return fmt.Errorf("name resolution errors: %s", strings.Join(errs, "; "))
	}

	return nil
}

func (s *workService) AddTagToWorkByName(ctx context.Context, claims *auth.Claims, workID uint, tagName string) error {
	if err := s._ensureAdmin(claims); err != nil {
		return err
	}
	if _, err := s.workRepo.GetByID(ctx, workID); err != nil {
		if errors.Is(err, apperrors.ErrWorkNotFound) {
			return apperrors.ErrWorkNotFound
		}
		return fmt.Errorf("failed to find work %d: %w", workID, err)
	}
	tag, err := s.tagRepo.GetOrCreateTag(ctx, tagName)
	if err != nil {
		return fmt.Errorf("failed to get or create tag '%s': %w", tagName, err)
	}
	return s.workRepo.AddTagToWork(ctx, workID, tag.ID)
}

func (s *workService) RemoveTagFromWorkByName(ctx context.Context, claims *auth.Claims, workID uint, tagName string) error {
	if err := s._ensureAdmin(claims); err != nil {
		return err
	}
	if _, err := s.workRepo.GetByID(ctx, workID); err != nil {
		if errors.Is(err, apperrors.ErrWorkNotFound) {
			return apperrors.ErrWorkNotFound
		}
		return fmt.Errorf("failed to find work %d: %w", workID, err)
	}
	tag, err := s.tagRepo.GetByName(ctx, tagName)
	if err != nil {
		if errors.Is(err, apperrors.ErrTagNotFound) {
			return apperrors.ErrTagNotFound
		}
		return fmt.Errorf("failed to find tag '%s': %w", tagName, err)
	}
	return s.workRepo.RemoveTagFromWork(ctx, workID, tag.ID)
}

func (s *workService) ReplaceWorkTagsByNames(ctx context.Context, claims *auth.Claims, workID uint, tagNames []string) error {
	if err := s._ensureAdmin(claims); err != nil {
		return err
	}
	if _, err := s.workRepo.GetByID(ctx, workID); err != nil {
		if errors.Is(err, apperrors.ErrWorkNotFound) {
			return apperrors.ErrWorkNotFound
		}
		return fmt.Errorf("failed to find work %d: %w", workID, err)
	}
	var tagIDs []string
	for _, name := range tagNames {
		tag, err := s.tagRepo.GetOrCreateTag(ctx, name)
		if err != nil {
			return fmt.Errorf("failed to process tag '%s': %w", name, err)
		}
		tagIDs = append(tagIDs, tag.ID)
	}
	return s.workRepo.ReplaceWorkTags(ctx, workID, tagIDs)
}

func (s *workService) AddVAToWorkByName(ctx context.Context, claims *auth.Claims, workID uint, vaName string) error {
	if err := s._ensureAdmin(claims); err != nil {
		return err
	}
	if _, err := s.workRepo.GetByID(ctx, workID); err != nil {
		if errors.Is(err, apperrors.ErrWorkNotFound) {
			return apperrors.ErrWorkNotFound
		}
		return fmt.Errorf("failed to find work %d: %w", workID, err)
	}
	va, err := s.vaRepo.GetOrCreateVA(ctx, vaName)
	if err != nil {
		return fmt.Errorf("failed to get or create VA '%s': %w", vaName, err)
	}
	return s.workRepo.AddVAToWork(ctx, workID, va.ID)
}

func (s *workService) RemoveVAFromWorkByName(ctx context.Context, claims *auth.Claims, workID uint, vaName string) error {
	if err := s._ensureAdmin(claims); err != nil {
		return err
	}
	if _, err := s.workRepo.GetByID(ctx, workID); err != nil {
		if errors.Is(err, apperrors.ErrWorkNotFound) {
			return apperrors.ErrWorkNotFound
		}
		return fmt.Errorf("failed to find work %d: %w", workID, err)
	}
	va, err := s.vaRepo.GetByName(ctx, vaName)
	if err != nil {
		if errors.Is(err, apperrors.ErrVANotFound) {
			return apperrors.ErrVANotFound
		}
		return fmt.Errorf("failed to find VA '%s': %w", vaName, err)
	}
	return s.workRepo.RemoveVAFromWork(ctx, workID, va.ID)
}

func (s *workService) ReplaceWorkVAsByNames(ctx context.Context, claims *auth.Claims, workID uint, vaNames []string) error {
	if err := s._ensureAdmin(claims); err != nil {
		return err
	}
	if _, err := s.workRepo.GetByID(ctx, workID); err != nil {
		if errors.Is(err, apperrors.ErrWorkNotFound) {
			return apperrors.ErrWorkNotFound
		}
		return fmt.Errorf("failed to find work %d: %w", workID, err)
	}
	var vaIDs []string
	for _, name := range vaNames {
		va, err := s.vaRepo.GetOrCreateVA(ctx, name)
		if err != nil {
			return fmt.Errorf("failed to process VA '%s': %w", name, err)
		}
		vaIDs = append(vaIDs, va.ID)
	}
	return s.workRepo.ReplaceWorkVAs(ctx, workID, vaIDs)
}

// --- Helper methods for TriggerScrapeForWorkByAdmin ---

func (s *workService) _updateWorkScalarMetadata(work *models.Work, scrapedData *scraper_dto.ScrapedWorkData) (changed bool) {
	if scrapedData.Title != "" && work.Title != scrapedData.Title {
		work.Title = scrapedData.Title
		changed = true
	}
	// Handle OriginalID (now *string)
	if scrapedData.OriginalID != "" {
		if work.OriginalID == nil || *work.OriginalID != scrapedData.OriginalID {
			work.OriginalID = &scrapedData.OriginalID
			changed = true
		}
	} else {
		if work.OriginalID != nil {
			work.OriginalID = nil
			changed = true
		}
	}

	var finalWorkType string
	if work.OriginalID != nil && *work.OriginalID != "" {
		matches := rjCodePattern.FindStringSubmatch(*work.OriginalID)
		if len(matches) > 1 {
			finalWorkType = strings.ToUpper(matches[1])
		}
	}
	if finalWorkType == "" && scrapedData.WorkType != "" {
		wtUpper := strings.ToUpper(scrapedData.WorkType)
		if wtUpper == "RJ" || wtUpper == "VJ" || wtUpper == "BJ" {
			finalWorkType = wtUpper
		}
	}
	if finalWorkType != "RJ" && finalWorkType != "VJ" && finalWorkType != "BJ" {
		finalWorkType = ""
	}
	// Handle WorkType (now *string)
	if finalWorkType != "" {
		if work.WorkType == nil || *work.WorkType != finalWorkType {
			work.WorkType = &finalWorkType
			changed = true
		}
	} else {
		if work.WorkType != nil {
			work.WorkType = nil
			changed = true
		}
	}

	// Handle AgeRating (now *string) and NSFW
	if scrapedData.AgeRating != "" {
		if work.AgeRating == nil || *work.AgeRating != scrapedData.AgeRating {
			ageRatingVal := scrapedData.AgeRating
			work.AgeRating = &ageRatingVal

			// 根据 AgeRating 设置 NSFW 字段
			var nsfw bool
			if ageRatingVal == "adult" || ageRatingVal == "r15" {
				nsfw = true
			} else if ageRatingVal == "general" {
				nsfw = false
			} else {
				// 对于未知的 AgeRating，保持 NSFW 为 nil
				work.NSFW = nil
			}

			if ageRatingVal == "adult" || ageRatingVal == "r15" || ageRatingVal == "general" {
				work.NSFW = &nsfw
			}

			changed = true
		}
	} else {
		if work.AgeRating != nil {
			work.AgeRating = nil
			changed = true
		}
	}

	// Handle ReleaseDate (now *string)
	if scrapedData.ReleaseDate != "" {
		releaseDateStr := scrapedData.ReleaseDate
		if work.ReleaseDate == nil || *work.ReleaseDate != releaseDateStr {
			work.ReleaseDate = &releaseDateStr
			changed = true
		}
	} else {
		if work.ReleaseDate != nil {
			work.ReleaseDate = nil
			changed = true
		}
	}

	// Handle DLCount (now *int64)
	if scrapedData.DLCount > 0 { // Consider if 0 is a valid value or means "not set"
		dlCountVal := scrapedData.DLCount
		if work.DlCount == nil || *work.DlCount != dlCountVal {
			work.DlCount = &dlCountVal
			changed = true
		}
	} else { // If 0 means "not set" or should clear existing
		if work.DlCount != nil {
			work.DlCount = nil
			changed = true
		}
	}

	// Handle Price (now *int64)
	if scrapedData.Price > 0 { // Consider if 0 is a valid value or means "not set"
		priceVal := scrapedData.Price
		if work.Price == nil || *work.Price != priceVal {
			work.Price = &priceVal
			changed = true
		}
	} else { // If 0 means "not set" or should clear existing
		if work.Price != nil {
			work.Price = nil
			changed = true
		}
	}

	// Handle Duration (now *int64)
	if scrapedData.Duration > 0 { // Consider if 0 is a valid value or means "not set"
		durationVal := scrapedData.Duration
		if work.Duration == nil || *work.Duration != durationVal {
			work.Duration = &durationVal
			changed = true
		}
	} else { // If 0 means "not set" or should clear existing
		if work.Duration != nil {
			work.Duration = nil
			changed = true
		}
	}

	// Use scraped rating data if available and valid
	// Handle RateAverage2DP (*float64) and RateCount (*int64)
	if scrapedData.RatingCount >= 0 { // RatingCount can be 0
		if work.RateAverage2DP == nil || *work.RateAverage2DP != scrapedData.RatingAverage {
			avg := scrapedData.RatingAverage
			work.RateAverage2DP = &avg
			changed = true
		}
		if work.RateCount == nil || *work.RateCount != scrapedData.RatingCount {
			count := scrapedData.RatingCount
			work.RateCount = &count
			changed = true
		}
	} else { // If RatingCount is not set (e.g. < 0, though typically it's >=0 from scraper)
		if work.RateAverage2DP != nil {
			work.RateAverage2DP = nil
			changed = true
		}
		if work.RateCount != nil {
			work.RateCount = nil
			changed = true
		}
	}

	// Handle ReviewCount (now *int64)
	if scrapedData.ReviewCount > 0 { // Consider if 0 is a valid value or means "not set"
		reviewCountVal := scrapedData.ReviewCount
		if work.ReviewCount == nil || *work.ReviewCount != reviewCountVal {
			work.ReviewCount = &reviewCountVal
			changed = true
		}
	} else { // If 0 means "not set" or should clear existing
		if work.ReviewCount != nil {
			work.ReviewCount = nil
			changed = true
		}
	}

	// Handle RateCountDetail (*string from []scraper_dto.ScrapedRateCountDetailItem)
	if len(scrapedData.RateCountDetail) > 0 {
		// Convert to JSON string for storage
		rateDetailJSON, err := json.Marshal(scrapedData.RateCountDetail)
		if err == nil {
			rateDetailStr := string(rateDetailJSON)
			if work.RateCountDetail == nil || *work.RateCountDetail != rateDetailStr {
				work.RateCountDetail = &rateDetailStr
				changed = true
			}
		} else {
			s.logger.WarnContext(context.Background(), "Failed to marshal scraped rate count detail data", "work_id", work.ID, "error", err)
		}
	} else { // If scrapedData.RateCountDetail is empty, clear work.RateCountDetail if it's not already empty
		if work.RateCountDetail != nil {
			work.RateCountDetail = nil
			changed = true
		}
	}

	// Handle Rank (now models.RankList from []scraper_dto.ScrapedRankItem)
	if len(scrapedData.Rank) > 0 {
		var newRankList models.RankList
		// Attempt to convert []scraper_dto.ScrapedRankItem to models.RankList
		// This typically involves marshalling to JSON then using the Scan method of models.RankList
		rankJSON, err := json.Marshal(scrapedData.Rank)
		if err == nil {
			if scanErr := newRankList.Scan(rankJSON); scanErr == nil {
				// Compare newRankList with work.Rank for changes
				currentRankJSON, _ := json.Marshal(work.Rank)    // Marshal current rank for comparison
				if string(rankJSON) != string(currentRankJSON) { // Compare JSON strings
					work.Rank = newRankList
					changed = true
				}
			} else {
				s.logger.WarnContext(context.Background(), "Failed to scan scraped rank data into models.RankList", "work_id", work.ID, "error", scanErr)
			}
		} else {
			s.logger.WarnContext(context.Background(), "Failed to marshal scraped rank data for RankList conversion", "work_id", work.ID, "error", err)
		}
	} else { // If scrapedData.Rank is empty, clear work.Rank if it's not already empty
		if len(work.Rank) > 0 {
			work.Rank = models.RankList{} // Assign an empty RankList
			changed = true
		}
	}
	return changed
}

func (s *workService) _updateWorkCircleFromScraped(ctx context.Context, work *models.Work, scrapedCircle *circle_dto.CircleNameDTO) (changed bool, err error) { // Changed to circle_dto.CircleNameDTO
	if scrapedCircle != nil && scrapedCircle.Name != "" {
		circle, circleErr := s.circleRepo.GetOrCreateCircle(ctx, scrapedCircle.Name)
		if circleErr != nil {
			s.logger.ErrorContext(ctx, "Failed to get/create circle during scrape processing", "work_id", work.ID, "circle_name", scrapedCircle.Name, "error", circleErr)
			return false, fmt.Errorf("processing circle '%s': %w", scrapedCircle.Name, circleErr)
		}
		if work.Circle == nil || work.Circle.ID != circle.ID {
			work.Circle = circle
			return true, nil
		}
	} else if work.Circle != nil {
		work.Circle = nil
		return true, nil
	}
	return false, nil
}

func (s *workService) _updateWorkTagsFromScraped(ctx context.Context, work *models.Work, scrapedTags []tag_dto.TagNameDTO) (changed bool, err error) { // Changed to tag_dto.TagNameDTO
	tagNames := make([]string, 0, len(scrapedTags))
	for _, t := range scrapedTags {
		if t.Name != "" {
			tagNames = append(tagNames, t.Name)
		}
	}

	var tagIDs []string
	if len(tagNames) > 0 {
		for _, tagName := range tagNames {
			tag, errTag := s.tagRepo.GetOrCreateTag(ctx, tagName)
			if errTag != nil {
				s.logger.ErrorContext(ctx, "Failed to get or create tag during scrape processing", "work_id", work.ID, "tag_name", tagName, "error", errTag)
				continue
			}
			tagIDs = append(tagIDs, tag.ID)
		}
	}

	currentTags, errCurrTags := s.workRepo.GetWorkTags(ctx, work.ID)
	tagsActuallyChanged := false
	if errCurrTags != nil {
		s.logger.WarnContext(ctx, "Failed to get current tags for comparison, assuming changed", "work_id", work.ID, "error", errCurrTags)
		tagsActuallyChanged = true
	} else {
		if len(currentTags) != len(tagIDs) {
			tagsActuallyChanged = true
		} else {
			currentTagIDMap := make(map[string]bool)
			for _, ct := range currentTags {
				currentTagIDMap[ct.ID] = true
			}
			for _, newTagID := range tagIDs {
				if !currentTagIDMap[newTagID] {
					tagsActuallyChanged = true
					break
				}
			}
		}
	}

	if tagsActuallyChanged {
		if errReplace := s.workRepo.ReplaceWorkTags(ctx, work.ID, tagIDs); errReplace != nil {
			s.logger.ErrorContext(ctx, "Failed to replace work tags during scrape processing", "work_id", work.ID, "error", errReplace)
			return false, fmt.Errorf("replacing work tags for work %d: %w", work.ID, errReplace)
		}
		s.logger.InfoContext(ctx, "Successfully replaced tags for work", "work_id", work.ID, "tag_count", len(tagIDs))
		return true, nil
	}
	return false, nil
}

func (s *workService) _updateWorkVAsFromScraped(ctx context.Context, work *models.Work, scrapedVAs []va_dto.VANameDTO) (changed bool, err error) { // Changed to va_dto.VANameDTO
	vaNames := make([]string, 0, len(scrapedVAs))
	for _, v := range scrapedVAs {
		if v.Name != "" {
			vaNames = append(vaNames, v.Name)
		}
	}
	var vaDBIDs []string
	if len(vaNames) > 0 {
		for _, vaName := range vaNames {
			va, errVA := s.vaRepo.GetOrCreateVA(ctx, vaName)
			if errVA != nil {
				s.logger.ErrorContext(ctx, "Failed to get or create VA during scrape processing", "work_id", work.ID, "va_name", vaName, "error", errVA)
				continue
			}
			vaDBIDs = append(vaDBIDs, va.ID)
		}
	}

	currentVAs, errCurrVAs := s.workRepo.GetWorkVAs(ctx, work.ID)
	vasActuallyChanged := false
	if errCurrVAs != nil {
		s.logger.WarnContext(ctx, "Failed to get current VAs for comparison, assuming changed", "work_id", work.ID, "error", errCurrVAs)
		vasActuallyChanged = true
	} else {
		if len(currentVAs) != len(vaDBIDs) {
			vasActuallyChanged = true
		} else {
			currentVAIDMap := make(map[string]bool)
			for _, cv := range currentVAs {
				currentVAIDMap[cv.ID] = true
			}
			for _, newVAID := range vaDBIDs {
				if !currentVAIDMap[newVAID] {
					vasActuallyChanged = true
					break
				}
			}
		}
	}

	if vasActuallyChanged {
		if errReplace := s.workRepo.ReplaceWorkVAs(ctx, work.ID, vaDBIDs); errReplace != nil {
			s.logger.ErrorContext(ctx, "Failed to replace work VAs during scrape processing", "work_id", work.ID, "error", errReplace)
			return false, fmt.Errorf("replacing work VAs for work %d: %w", work.ID, errReplace)
		}
		s.logger.InfoContext(ctx, "Successfully replaced VAs for work", "work_id", work.ID, "va_count", len(vaDBIDs))
		return true, nil
	}
	return false, nil
}

// _processAndSaveCover has been moved to MediaService as ProcessAndStoreCover

// --- Manual Scrape Triggers (Admin) ---
func (s *workService) TriggerScrapeForWorkByAdmin(ctx context.Context, claims *auth.Claims, workID uint, req ports.ScrapeOptions) error {
	// 先检查是否为系统调用（claims 为 nil）
	if claims == nil {
		s.logger.InfoContext(ctx, "System-initiated scrape for work", "work_id", workID)
	} else {
		// 如果有 claims，检查管理员权限
		if err := s._ensureAdmin(claims); err != nil {
			return err
		}
		s.logger.InfoContext(ctx, "Admin-initiated scrape for work", "work_id", workID, "admin_user", claims.Username)
	}

	if s.scraperService == nil {
		s.logger.ErrorContext(ctx, "Scraper service is not available/configured")
		return errors.New("scraper service is not available/configured")
	}

	work, err := s.workRepo.GetByID(ctx, workID)
	if err != nil {
		if errors.Is(err, apperrors.ErrWorkNotFound) {
			return apperrors.ErrWorkNotFound
		}
		s.logger.ErrorContext(ctx, "Failed to get work for scraping", "work_id", workID, "error", err)
		return fmt.Errorf("failed to retrieve work %d for scraping: %w", workID, err)
	}

	var originalIDToScrape string
	if work.OriginalID != nil && *work.OriginalID != "" {
		originalIDToScrape = *work.OriginalID
	} else {
		s.logger.WarnContext(ctx, "Work has no OriginalID, attempting to infer from PathInStorage for scraping", "work_id", workID, "path_in_storage", work.PathInStorage)
		if work.PathInStorage != "" {
			matches := rjCodePattern.FindStringSubmatch(work.PathInStorage)
			if len(matches) > 0 {
				originalIDToScrape = matches[0]
				s.logger.InfoContext(ctx, "Inferred OriginalID for scraping from PathInStorage", "work_id", workID, "inferred_original_id", originalIDToScrape)
			} else {
				s.logger.ErrorContext(ctx, "Work has no OriginalID and could not be inferred from PathInStorage, cannot scrape", "work_id", workID, "path_in_storage", work.PathInStorage)
				return fmt.Errorf("work ID %d has no OriginalID and PathInStorage is empty, cannot scrape", workID)
			}
		} else {
			s.logger.ErrorContext(ctx, "Work has no OriginalID and PathInStorage is empty, cannot scrape", "work_id", workID)
			return fmt.Errorf("work ID %d has no OriginalID and PathInStorage is empty, cannot scrape", workID)
		}
	}

	s.logger.InfoContext(ctx, "Calling ScraperService.ScrapeWorkDataAndCover", "work_id", workID, "original_id_to_scrape", originalIDToScrape)
	scrapedData, mainCoverBytes, samCoverBytes, scrapeErr := s.scraperService.ScrapeWorkDataAndCover(ctx, originalIDToScrape, req)

	var overallChanged bool
	if scrapedData != nil {
		if req.ScrapeMetadata {
			if changed := s._updateWorkScalarMetadata(work, scrapedData); changed {
				overallChanged = true
			}
			if changed, errCircle := s._updateWorkCircleFromScraped(ctx, work, scrapedData.Circle); errCircle != nil {
				s.logger.ErrorContext(ctx, "Error updating work circle from scrape", "work_id", workID, "error", errCircle)
			} else if changed {
				overallChanged = true
			}
			if changed, errTags := s._updateWorkTagsFromScraped(ctx, work, scrapedData.Tags); errTags != nil {
				s.logger.ErrorContext(ctx, "Error updating work tags from scrape", "work_id", workID, "error", errTags)
			} else if changed {
				overallChanged = true
			}
			if changed, errVAs := s._updateWorkVAsFromScraped(ctx, work, scrapedData.VAS); errVAs != nil {
				s.logger.ErrorContext(ctx, "Error updating work VAs from scrape", "work_id", workID, "error", errVAs)
			} else if changed {
				overallChanged = true
			}
		}

		if req.DownloadCover {
			if len(mainCoverBytes) > 0 {
				_, errMainCover := s.mediaService.ProcessAndStoreCover(ctx, originalIDToScrape, mainCoverBytes, ports.CoverTypeMain)
				if errMainCover != nil {
					s.logger.ErrorContext(ctx, "Error processing/saving main cover via MediaService", "work_id", workID, "error", errMainCover)
					if scrapeErr == nil {
						scrapeErr = errMainCover
					}
				}
			} else {
				s.logger.InfoContext(ctx, "No main cover bytes to process for work", "work_id", workID)
			}

			if len(samCoverBytes) > 0 {
				_, errSamCover := s.mediaService.ProcessAndStoreCover(ctx, originalIDToScrape, samCoverBytes, ports.CoverTypeSam)
				if errSamCover != nil {
					s.logger.ErrorContext(ctx, "Error processing/saving SAM cover via MediaService", "work_id", workID, "error", errSamCover)
					if scrapeErr == nil {
						scrapeErr = errSamCover
					}
				}
			} else {
				s.logger.InfoContext(ctx, "No SAM cover bytes to process for work", "work_id", workID)
			}
		}
	} else if scrapeErr == nil {
		scrapeErr = errors.New("scraper returned no data without explicit error")
		s.logger.ErrorContext(ctx, "Scraping returned no data without explicit error", "work_id", workID, "original_id", originalIDToScrape)
	}

	if overallChanged {
		if errUpdate := s.workRepo.Update(ctx, work); errUpdate != nil {
			s.logger.ErrorContext(ctx, "Failed to update work record after processing scrape data", "work_id", workID, "error", errUpdate)
			return fmt.Errorf("failed to update work %d after scrape: %w", workID, errUpdate)
		}
		s.logger.InfoContext(ctx, "Work record updated successfully after scrape", "work_id", workID)
	} else if scrapeErr == nil {
		s.logger.InfoContext(ctx, "Scraping completed, no database changes required for work", "work_id", workID)
	}

	if scrapeErr != nil && scrapedData == nil {
		return fmt.Errorf("scraping failed for work %d (originalID: %s): %w", workID, originalIDToScrape, scrapeErr)
	}
	return scrapeErr
}

func (s *workService) TriggerScrapeForAllWorksByAdmin(ctx context.Context, claims *auth.Claims, req ports.ScrapeOptions) error {
	if err := s._ensureAdmin(claims); err != nil {
		if claims != nil {
			return err
		}
		s.logger.InfoContext(ctx, "System-initiated scrape for all works")
	} else {
		s.logger.InfoContext(ctx, "Admin-initiated scrape for ALL works", "admin_user", claims.Username)
	}

	allWorks, _, err := s.workRepo.List(ctx, database.ListWorksParams{PaginationParams: common_dto.PaginationParams{PageSize: 0}})
	if err != nil {
		s.logger.ErrorContext(ctx, "Failed to list all works for bulk scrape", "error", err)
		return fmt.Errorf("failed to list works for bulk scraping: %w", err)
	}

	if len(allWorks) == 0 {
		s.logger.InfoContext(ctx, "No works found to scrape.")
		return nil
	}

	s.logger.InfoContext(ctx, "Starting bulk scrape", "work_count", len(allWorks))

	var firstError error
	successCount := 0
	errorCount := 0

	for _, work := range allWorks {
		select {
		case <-ctx.Done():
			s.logger.InfoContext(ctx, "Bulk scrape cancelled", "reason", ctx.Err())
			if firstError == nil {
				firstError = ctx.Err()
			}
			s.logger.InfoContext(ctx, "Bulk scrape finished due to cancellation", "total_works_processed_before_cancel", successCount+errorCount, "successful_scrapes", successCount, "failed_scrapes", errorCount)
			return firstError
		default:
		}

		originalIDLog := "N/A"
		if work.OriginalID != nil {
			originalIDLog = *work.OriginalID
		}
		s.logger.InfoContext(ctx, "Processing work for bulk scrape", "work_id", work.ID, "original_id", originalIDLog)
		errScrape := s.TriggerScrapeForWorkByAdmin(ctx, claims, work.ID, req)

		if errScrape != nil {
			originalIDErrorLog := "N/A"
			if work.OriginalID != nil {
				originalIDErrorLog = *work.OriginalID
			}
			s.logger.ErrorContext(ctx, "Error scraping a work during bulk operation", "work_id", work.ID, "original_id", originalIDErrorLog, "error", errScrape)
			errorCount++
			if firstError == nil {
				firstError = errScrape
			}
		} else {
			successCount++
		}
	}

	s.logger.InfoContext(ctx, "Bulk scrape finished", "total_works_attempted", len(allWorks), "successful_scrapes", successCount, "failed_scrapes", errorCount)
	if firstError != nil {
		return fmt.Errorf("bulk scrape completed with %d errors, first error: %w", errorCount, firstError)
	}
	return nil
}

// GetWorkInfoByOriginalID implements the method for fetching detailed work information using originalID.
func (s *workService) GetWorkInfoByOriginalID(ctx context.Context, originalID string) (*work_dto.WorkDTO, error) {
	s.logger.InfoContext(ctx, "GetWorkInfoByOriginalID called", "originalID", originalID)

	work, err := s.workRepo.GetByOriginalID(ctx, originalID)
	if err != nil {
		if errors.Is(err, apperrors.ErrWorkNotFound) {
			return nil, apperrors.ErrWorkNotFound
		}
		s.logger.ErrorContext(ctx, "Failed to get work by originalID for GetWorkInfo", "original_id", originalID, "error", err)
		return nil, fmt.Errorf("failed to retrieve work '%s': %w", originalID, err)
	}

	resp := &work_dto.WorkDTO{
		ID:              work.ID,
		CreatedAt:       work.CreatedAt,
		UpdatedAt:       work.UpdatedAt,
		OriginalID:      work.OriginalID,
		WorkType:        work.WorkType,
		Title:           work.Title,
		AgeRating:       work.AgeRating,
		NSFW:            work.NSFW,
		ReleaseDate:     work.ReleaseDate,
		RateCount:       work.RateCount,
		RateAverage2DP:  work.RateAverage2DP,
		Rank:            work.Rank,
		LyricStatus:     work.LyricStatus,
		Language:        work.Language,
		Duration:        work.Duration,
		DlCount:         work.DlCount,
		Price:           work.Price,
		ReviewCount:     work.ReviewCount,
		RateCountDetail: work.RateCountDetail,
		StorageID:       work.StorageID,
		PathInStorage:   work.PathInStorage,
		Circle:          circle_dto.MapCircleModelToDTO(work.Circle), // Changed to use converter
		Tags:            tag_dto.MapTagModelsToDTOs(work.Tags),       // Changed to use converter
		VAs:             va_dto.MapVAModelsToDTOs(work.VAs),          // Changed to use converter
		UserRating:      nil,                                         // 用户评分初始为nil
	}

	if work.OriginalID != nil && *work.OriginalID != "" {
		originalIDStr := *work.OriginalID
		baseAPIPath := fmt.Sprintf("/api/v1/cover/%s", originalIDStr)
		mainURL := fmt.Sprintf("%s?type=main", baseAPIPath)
		samURL := fmt.Sprintf("%s?type=sam", baseAPIPath)
		resp.ImageMainURL = &mainURL
		resp.ImageSamURL = &samURL
	} else {
		resp.ImageMainURL = nil
		resp.ImageSamURL = nil
	}
	return resp, nil
}

// Add the following method to the workService struct implementation

// CountWorks returns the total number of works in the database
func (s *workService) CountWorks() (int64, error) {
	// Get a context with a short timeout
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Use the repository to count works (we'll just get the count from a list with no filters)
	_, total, err := s.workRepo.List(ctx, database.ListWorksParams{})
	if err != nil {
		s.logger.Error("Failed to count works", "error", err)
		return 0, fmt.Errorf("failed to count works: %w", err)
	}

	return total, nil
}
