package database

import (
	"context" // Added missing import
	"fmt"
	"log/slog"
	"os"
	"path/filepath"
	"time"

	"github.com/Sakura-Byte/kikoeru-go/pkg/config"
	"gorm.io/driver/mysql"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/gorm/schema"
)

// NewGormLogger 创建一个适配 slog 的 GORM logger
type NewGormLogger struct {
	slogger *slog.Logger
	level   logger.LogLevel
	slowSQL time.Duration
}

func (l *NewGormLogger) LogMode(level logger.LogLevel) logger.Interface {
	newlogger := *l
	newlogger.level = level
	return &newlogger
}

func (l *NewGormLogger) Info(ctx context.Context, msg string, data ...interface{}) {
	if l.level >= logger.Info {
		l.slogger.InfoContext(ctx, msg, data...)
	}
}

func (l *NewGormLogger) Warn(ctx context.Context, msg string, data ...interface{}) {
	if l.level >= logger.Warn {
		l.slogger.WarnContext(ctx, msg, data...)
	}
}

func (l *NewGormLogger) Error(ctx context.Context, msg string, data ...interface{}) {
	if l.level >= logger.Error {
		l.slogger.ErrorContext(ctx, msg, data...)
	}
}

func (l *NewGormLogger) Trace(ctx context.Context, begin time.Time, fc func() (sql string, rowsAffected int64), err error) {
	if l.level <= logger.Silent {
		return
	}

	elapsed := time.Since(begin)
	sql, rows := fc()
	fields := []interface{}{
		slog.Duration("elapsed", elapsed),
		slog.Int64("rows", rows),
		slog.String("sql", sql),
	}

	if err != nil && err != gorm.ErrRecordNotFound { // gorm.ErrRecordNotFound 不是一个真正的错误
		l.slogger.ErrorContext(ctx, "DB_TRACE_ERROR", append(fields, slog.Any("error", err))...)
		return
	}

	if l.slowSQL > 0 && elapsed > l.slowSQL {
		l.slogger.WarnContext(ctx, "DB_TRACE_SLOW_SQL", fields...)
		return
	}

	if l.level >= logger.Info { // 通常 Trace 对应 slog.Debug
		l.slogger.DebugContext(ctx, "DB_TRACE", fields...)
	}
}

// ConnectDB 根据配置初始化并返回一个 GORM 数据库实例
func ConnectDB(cfg config.DatabaseConfig, appPaths config.PathConfig, slogLogger *slog.Logger) (*gorm.DB, error) {
	var dialector gorm.Dialector
	var err error

	gormLogLevel := logger.Warn // 默认 GORM 日志级别
	if slogLogger.Enabled(context.Background(), slog.LevelDebug) {
		gormLogLevel = logger.Info // 如果 slog 是 debug，GORM 也用 Info (Trace)
	}

	gormLogger := &NewGormLogger{
		slogger: slogLogger.With("module", "gorm"),
		level:   gormLogLevel,
		slowSQL: 200 * time.Millisecond, // 慢 SQL 阈值
	}

	switch cfg.Driver {
	case "sqlite":
		// 确保 SQLite 数据库文件所在的目录存在
		dbPath := cfg.SQLite.Path
		if !filepath.IsAbs(dbPath) {
			// 如果是相对路径，则相对于应用的数据目录
			if appPaths.DataDir == "" {
				return nil, fmt.Errorf("database.sqlite.path is relative but paths.data_dir is not configured")
			}
			dbPath = filepath.Join(appPaths.DataDir, dbPath)
		}
		dbDir := filepath.Dir(dbPath)
		if err := os.MkdirAll(dbDir, 0750); err != nil {
			return nil, fmt.Errorf("failed to create SQLite directory %s: %w", dbDir, err)
		}
		slogLogger.Info("Using SQLite database", "path", dbPath)
		dialector = sqlite.Open(dbPath)
	case "mysql":
		dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?%s",
			cfg.MySQL.User,
			cfg.MySQL.Password,
			cfg.MySQL.Host,
			cfg.MySQL.Port,
			cfg.MySQL.DBName,
			cfg.MySQL.Params,
		)
		slogLogger.Info("Using MySQL database", "host", cfg.MySQL.Host, "port", cfg.MySQL.Port, "dbname", cfg.MySQL.DBName)
		dialector = mysql.Open(dsn)
	default:
		return nil, fmt.Errorf("unsupported database driver: %s", cfg.Driver)
	}

	db, err := gorm.Open(dialector, &gorm.Config{
		Logger: gormLogger,
		NamingStrategy: schema.NamingStrategy{
			// TablePrefix: "t_", // 如果需要表前缀
			SingularTable: false, // 使用复数表名，例如 "users" 而不是 "user"，但我们通过 TableName() 方法指定
		},
		DisableForeignKeyConstraintWhenMigrating: true, // 迁移时禁用外键，由迁移脚本处理
	})

	if err != nil {
		return nil, fmt.Errorf("failed to connect to database (%s): %w", cfg.Driver, err)
	}

	// 配置连接池 (GORM 使用 database/sql 的连接池)
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)
	sqlDB.SetConnMaxLifetime(time.Hour)
	if cfg.BusyTimeout > 0 && cfg.Driver == "sqlite" {
		// 对于 SQLite，可以设置 PRAGMA busy_timeout
		// GORM 的 SQLite 驱动可能已经处理了，或者可以通过 DSN 设置
		// 例如: "file.db?_busy_timeout=5000"
		// 这里我们也可以直接执行
		if err := db.Exec(fmt.Sprintf("PRAGMA busy_timeout = %d;", cfg.BusyTimeout)).Error; err != nil {
			slogLogger.Warn("Failed to set PRAGMA busy_timeout for SQLite", "error", err)
		}
	}

	slogLogger.Info("Database connection established successfully", "driver", cfg.Driver)
	return db, nil
}
