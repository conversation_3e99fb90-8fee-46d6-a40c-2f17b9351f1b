package local

import (
	"context"
	"errors"
	"fmt"
	"io"
	"io/fs"
	"log/slog"
	"mime"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/Sakura-Byte/kikoeru-go/pkg/http_range"
	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
	kikdrv "github.com/Sakura-Byte/kikoeru-go/pkg/storage/driver"
	"github.com/mitchellh/mapstructure"

	"github.com/alist-org/times"

	_ "golang.org/x/image/webp"
)

type Local struct {
	StorageSource models.StorageSource
	Addition
	logger *slog.Logger
}

// Config returns the driver's static configuration.
func (d *Local) Config() kikdrv.Config {
	return kikdrv.Config{
		Name:              DriverName,
		DisplayName:       DriverDisplayName,
		DefaultDriverRoot: "",
		DownloadOptions:   []string{kikdrv.DownloadStrategyProxy},
		Params:            kikdrv.ParamsFromStruct(Addition{}),
	}
}

func (d *Local) Init(ctx context.Context) error {
	rootPathToValidate := d.Addition.RootFolderPath

	// Allow empty RootFolderPath during driver definition retrieval
	// This happens when GetDriverDefinitions() creates temporary instances
	if rootPathToValidate == "" {
		if d.logger != nil {
			d.logger.InfoContext(ctx, "Local driver Init called with empty RootFolderPath, likely for driver definition retrieval", "storage_id", d.StorageSource.ID)
		}
		return nil // Allow initialization to succeed for definition retrieval
	}

	if _, err := os.Stat(rootPathToValidate); os.IsNotExist(err) {
		errNotExist := fmt.Errorf("root folder '%s' does not exist: %w", rootPathToValidate, err)
		if d.logger != nil {
			d.logger.ErrorContext(ctx, "Local driver Init failed", "error", errNotExist, "storage_id", d.StorageSource.ID)
		}
		return errNotExist
	} else if err != nil {
		errStat := fmt.Errorf("failed to stat root folder '%s': %w", rootPathToValidate, err)
		if d.logger != nil {
			d.logger.ErrorContext(ctx, "Local driver Init failed", "error", errStat, "storage_id", d.StorageSource.ID)
		}
		return errStat
	}

	absPath, err := filepath.Abs(rootPathToValidate)
	if err != nil {
		errAbs := fmt.Errorf("failed to get absolute path for '%s': %w", rootPathToValidate, err)
		if d.logger != nil {
			d.logger.ErrorContext(ctx, "Local driver Init failed", "error", errAbs, "storage_id", d.StorageSource.ID)
		}
		return errAbs
	}
	d.Addition.RootFolderPath = absPath

	if d.logger != nil {
		d.logger.InfoContext(ctx, "Local driver initialized", "effective_root_path", d.Addition.RootFolderPath, "storage_id", d.StorageSource.ID)
	}
	return nil
}

func (d *Local) Drop(ctx context.Context) error          { return nil }
func (d *Local) GetAddition() interface{}                { return &d.Addition }
func (d *Local) SetStorage(storage models.StorageSource) { d.StorageSource = storage }
func (d *Local) GetStorage() *models.StorageSource       { return &d.StorageSource }

func (d *Local) getAbsPath(relativePath string) (string, error) {
	if d.Addition.RootFolderPath == "" {
		return "", errors.New("local driver RootFolderPath is not configured (should be set by Init from Addition)")
	}

	// Handle root path by trimming leading slashes, making it safe for `filepath.Join`.
	// This handles "/", "\", "/path", and "\path" correctly.
	cleanedRelativePath := filepath.Clean(strings.TrimLeft(relativePath, `/\`))

	if strings.Contains(cleanedRelativePath, "..") {
		parts := strings.Split(cleanedRelativePath, string(filepath.Separator))
		level := 0
		for _, part := range parts {
			if part == ".." {
				level--
			} else if part != "." && part != "" {
				level++
			}
			if level < 0 {
				return "", fmt.Errorf("%w: path '%s' attempts to traverse above root", kikdrv.ErrInvalidPath, relativePath)
			}
		}
	}

	absPath := filepath.Join(d.Addition.RootFolderPath, cleanedRelativePath)
	rootPrefix := filepath.Clean(d.Addition.RootFolderPath)
	absCleaned := filepath.Clean(absPath)

	if rootPrefix == string(filepath.Separator) {
		if !strings.HasPrefix(absCleaned, rootPrefix) {
			return "", fmt.Errorf("%w: resolved path '%s' is outside of root folder '%s'", kikdrv.ErrInvalidPath, absPath, d.Addition.RootFolderPath)
		}
	} else {
		if !strings.HasPrefix(absCleaned+string(filepath.Separator), rootPrefix+string(filepath.Separator)) && absCleaned != rootPrefix {
			return "", fmt.Errorf("%w: resolved path '%s' is outside of root folder '%s'", kikdrv.ErrInvalidPath, absPath, d.Addition.RootFolderPath)
		}
	}
	return absPath, nil
}

func (d *Local) List(ctx context.Context, dir kikdrv.AlistObj, args kikdrv.AlistListArgs) ([]kikdrv.AlistObj, error) {
	absDirToList, err := d.getAbsPath(dir.GetPath())
	if err != nil {
		d.logger.ErrorContext(ctx, "List: Invalid path resolution", "relative_path", dir.GetPath(), "error", err)
		return nil, err
	}

	d.logger.DebugContext(ctx, "Listing directory (Local)", "abs_path", absDirToList, "rel_path_from_obj", dir.GetPath())
	rawFiles, err := os.ReadDir(absDirToList)
	if err != nil {
		d.logger.ErrorContext(ctx, "List: Failed to read directory", "abs_path", absDirToList, "error", err)
		if os.IsNotExist(err) {
			return nil, kikdrv.ErrFileNotFound
		}
		return nil, err
	}

	var files []kikdrv.AlistObj
	for _, fEntry := range rawFiles {
		fInfo, errInfo := fEntry.Info()
		if errInfo != nil {
			d.logger.WarnContext(ctx, "Failed to get FileInfo in List", "filename", fEntry.Name(), "path", absDirToList, "error", errInfo)
			continue
		}
		if !d.Addition.ShowHidden && strings.HasPrefix(fInfo.Name(), ".") {
			continue
		}
		files = append(files, d.fileInfoToObj(fInfo, dir.GetPath()))
	}
	return files, nil
}

func (d *Local) fileInfoToObj(f fs.FileInfo, parentRelPath string) kikdrv.AlistObj {
	objRelPath := filepath.ToSlash(filepath.Join(parentRelPath, f.Name()))
	isFolder := f.IsDir()
	var size int64
	if !isFolder {
		size = f.Size()
	}

	var ctime time.Time
	absPath := filepath.Join(d.Addition.RootFolderPath, objRelPath)
	t, errStat := times.Stat(absPath)
	if errStat == nil && t.HasBirthTime() {
		ctime = t.BirthTime()
	} else {
		ctime = f.ModTime()
	}

	return &kikdrv.AlistObject{
		ID:       objRelPath, // For local files, path can serve as ID
		Path:     objRelPath,
		Name:     f.Name(),
		Modified: f.ModTime(),
		Ctime:    ctime,
		Size:     size,
		IsFolder: isFolder,
	}
}

func (d *Local) Get(ctx context.Context, path string) (kikdrv.AlistObj, error) {
	absPath, err := d.getAbsPath(path)
	if err != nil {
		d.logger.ErrorContext(ctx, "Get: Invalid path resolution", "relative_path", path, "error", err)
		return nil, err
	}
	d.logger.DebugContext(ctx, "Getting item (Local)", "abs_path", absPath, "rel_path_input", path)

	f, err := os.Stat(absPath)
	if err != nil {
		if os.IsNotExist(err) {
			return nil, kikdrv.ErrFileNotFound
		}
		d.logger.ErrorContext(ctx, "Get: Failed to stat file/dir", "abs_path", absPath, "error", err)
		return nil, err
	}

	parentRelPath := filepath.ToSlash(filepath.Dir(path))
	if path == "." || path == "/" || parentRelPath == "." || parentRelPath == "/" {
		parentRelPath = "" // For items at root, parentRelPath is effectively empty for join purposes
	}

	return d.fileInfoToObj(f, parentRelPath), nil
}

// Link for Local driver returns a special file:// URL with the absolute path.
func (d *Local) Link(ctx context.Context, file kikdrv.AlistObj, args kikdrv.AlistLinkArgs) (*kikdrv.AlistLink, error) {
	absPath, err := d.getAbsPath(file.GetPath())
	if err != nil {
		d.logger.ErrorContext(ctx, "Link: Failed to get absolute path for local file", "relative_path", file.GetPath(), "error", err)
		return nil, fmt.Errorf("failed to resolve absolute path for link: %w", err)
	}

	// For local files, the "link" is essentially its direct file path.
	// We can use a special scheme like "file://" to indicate this to the FileSystemService.
	// Note: This URL is not directly servable over HTTP by itself but signals FileSystemService to serve it.
	fileURL := "file://" + absPath
	d.logger.InfoContext(ctx, "Link method called for Local driver; providing direct file path URL.", "path", file.GetPath(), "abs_path_url", fileURL)

	return &kikdrv.AlistLink{
		URL: fileURL,
		// No specific headers or expiration needed for direct local file access this way.
	}, nil
}

// Proxy method for Local driver handles file:// URLs by reading local files directly
func (d *Local) Proxy(ctx context.Context, file kikdrv.AlistObj, httpRange string) (io.ReadCloser, string, int64, int64, int, error) {
	absPath, err := d.getAbsPath(file.GetPath())
	if err != nil {
		d.logger.ErrorContext(ctx, "Proxy: Failed to get absolute path for local file", "relative_path", file.GetPath(), "error", err)
		return nil, "", 0, 0, http.StatusInternalServerError, fmt.Errorf("failed to resolve absolute path: %w", err)
	}

	// Check if file exists and get its info
	fileInfo, err := os.Stat(absPath)
	if err != nil {
		if os.IsNotExist(err) {
			return nil, "", 0, 0, http.StatusNotFound, kikdrv.ErrFileNotFound
		}
		d.logger.ErrorContext(ctx, "Proxy: Failed to stat local file", "abs_path", absPath, "error", err)
		return nil, "", 0, 0, http.StatusInternalServerError, err
	}

	if fileInfo.IsDir() {
		return nil, "", 0, 0, http.StatusBadRequest, kikdrv.ErrNotAFile
	}

	originalSize := fileInfo.Size()

	// Determine content type from file extension
	contentType := mime.TypeByExtension(filepath.Ext(absPath))
	if contentType == "" {
		contentType = "application/octet-stream"
	}

	// Handle range requests
	var rangeStart int64 = 0
	var rangeLength int64 = originalSize
	httpStatus := http.StatusOK

	if httpRange != "" && originalSize > 0 {
		ranges, parseErr := http_range.ParseRange(httpRange, originalSize)
		if parseErr != nil {
			d.logger.WarnContext(ctx, "Malformed Range header, serving full content", "range_header", httpRange, "error", parseErr)
			// Don't return error, just serve full content
		} else if len(ranges) > 0 {
			hr := ranges[0] // Use the first range
			rangeStart = hr.Start
			rangeLength = hr.Length
			httpStatus = http.StatusPartialContent
			d.logger.DebugContext(ctx, "Serving partial content", "range_start", rangeStart, "range_length", rangeLength)
		}
	}

	// Open the file
	file_, err := os.Open(absPath)
	if err != nil {
		d.logger.ErrorContext(ctx, "Proxy: Failed to open local file", "abs_path", absPath, "error", err)
		return nil, "", 0, 0, http.StatusInternalServerError, err
	}

	// Seek to the start position if needed
	if rangeStart > 0 {
		_, err = file_.Seek(rangeStart, 0)
		if err != nil {
			file_.Close()
			d.logger.ErrorContext(ctx, "Proxy: Failed to seek in local file", "abs_path", absPath, "start", rangeStart, "error", err)
			return nil, "", 0, 0, http.StatusInternalServerError, err
		}
	}

	// Create a limited reader if we need partial content
	var reader io.ReadCloser = file_
	if rangeLength < originalSize {
		reader = &limitedReadCloser{
			Reader: io.LimitReader(file_, rangeLength),
			closer: file_,
		}
	}

	d.logger.DebugContext(ctx, "Successfully opened local file for streaming", "abs_path", absPath, "content_type", contentType, "original_size", originalSize, "range_length", rangeLength, "status", httpStatus)
	return reader, contentType, originalSize, rangeLength, httpStatus, nil
}

// limitedReadCloser wraps an io.LimitReader with a Close method
type limitedReadCloser struct {
	io.Reader
	closer io.Closer
}

func (lrc *limitedReadCloser) Close() error {
	return lrc.closer.Close()
}

func NewDriver(logger *slog.Logger, commonConfig kikdrv.AlistDriverCommonConfig, additionInput interface{}) (kikdrv.StorageDriver, error) {
	var addCfg Addition
	if additionInput != nil {
		if mapInput, ok := additionInput.(map[string]interface{}); ok {
			decoderConfig := &mapstructure.DecoderConfig{
				Result:  &addCfg,
				TagName: "mapstructure",
			}
			decoder, err := mapstructure.NewDecoder(decoderConfig)
			if err != nil {
				return nil, fmt.Errorf("failed to create mapstructure decoder for Local driver: %w", err)
			}
			if err := decoder.Decode(mapInput); err != nil {
				return nil, fmt.Errorf("failed to decode addition map into Local.Addition struct: %w", err)
			}
		} else {
			return nil, fmt.Errorf("invalid addition_config type for Local driver: expected map[string]interface{}, got %T", additionInput)
		}
	}

	// Allow empty RootFolderPath for driver definition retrieval (when additionInput is nil)
	// This is similar to how Google Drive driver handles missing configuration
	if addCfg.RootFolderPath == "" && additionInput != nil {
		logger.Error("Local Driver: RootFolderPath is required in Addition configuration and was not found.")
		return nil, errors.New("RootFolderPath is required for local driver and must be provided in 'addition' configuration")
	}

	instanceLogger := logger.With("driver_type", DriverName)
	if commonConfig.StorageID != 0 {
		instanceLogger = instanceLogger.With("storage_id", commonConfig.StorageID)
	}

	return &Local{
		Addition: addCfg,
		logger:   instanceLogger,
	}, nil
}

// Ensure Local implements all required interfaces
var _ kikdrv.StorageDriver = (*Local)(nil)
var _ kikdrv.Getter = (*Local)(nil)
var _ kikdrv.IRootPathGetter = (*Local)(nil)

// GetRootPath implements IRootPathGetter interface
func (d *Local) GetRootPath() string {
	return d.Addition.RootFolderPath
}
