package log

import (
	"context"
	"io"
	"log/slog"
	"os"
	"strings"

	"github.com/Sakura-Byte/kikoeru-go/pkg/config"
)

var (
	globalLogger *slog.Logger
)

// InitGlobalLogger 初始化全局日志记录器
// 应在配置加载后调用
func InitGlobalLogger(cfg config.LogConfig) {
	var level slog.Level
	switch strings.ToLower(cfg.Level) {
	case "debug":
		level = slog.LevelDebug
	case "info":
		level = slog.LevelInfo
	case "warn", "warning":
		level = slog.LevelWarn
	case "error", "err":
		level = slog.LevelError
	default:
		level = slog.LevelInfo // 默认级别
	}

	var handler slog.Handler
	opts := &slog.HandlerOptions{
		Level:     level,
		AddSource: true, // 添加源文件和行号信息
	}

	var output io.Writer = os.Stdout // 默认输出到标准输出

	switch strings.ToLower(cfg.Format) {
	case "json":
		handler = slog.NewJSONHandler(output, opts)
	case "text":
		fallthrough
	default:
		handler = slog.NewTextHandler(output, opts)
	}

	globalLogger = slog.New(handler)
	slog.SetDefault(globalLogger) // 将其设置为标准库 slog 的默认 logger
}

// ensureLogger 确保全局日志记录器已初始化
func ensureLogger(ctx context.Context) *slog.Logger {
	if globalLogger == nil {
		// 后备初始化，以防 InitGlobalLogger 未被显式调用
		InitGlobalLogger(config.LogConfig{Level: "info", Format: "text"})
		globalLogger.WarnContext(ctx, "Global logger accessed before explicit initialization. Using default settings.")
	}
	return globalLogger
}

// Debug 记录调试级别的日志，使用上下文
func Debug(ctx context.Context, msg string, args ...any) {
	ensureLogger(ctx).DebugContext(ctx, msg, args...)
}

// Info 记录信息级别的日志，使用上下文
func Info(ctx context.Context, msg string, args ...any) {
	ensureLogger(ctx).InfoContext(ctx, msg, args...)
}

// Warn 记录警告级别的日志，使用上下文
func Warn(ctx context.Context, msg string, args ...any) {
	ensureLogger(ctx).WarnContext(ctx, msg, args...)
}

// Error 记录错误级别的日志，使用上下文
func Error(ctx context.Context, msg string, args ...any) {
	ensureLogger(ctx).ErrorContext(ctx, msg, args...)
}

// With 创建一个带有预设字段的日志记录器
func With(args ...any) *slog.Logger {
	if globalLogger == nil {
		InitGlobalLogger(config.LogConfig{Level: "info", Format: "text"})
	}
	return globalLogger.With(args...)
}
