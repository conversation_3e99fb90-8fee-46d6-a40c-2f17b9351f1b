package log

import (
	"context"
	"io"
	"log/slog"
	"os"
	"strings"

	"github.com/Sakura-Byte/kikoeru-go/pkg/config" // 导入我们刚创建的配置包
)

var (
	globalLogger *slog.Logger
)

// InitGlobalLogger 初始化全局日志记录器
// 应在配置加载后调用
func InitGlobalLogger(cfg config.LogConfig) {
	var level slog.Level
	switch strings.ToLower(cfg.Level) {
	case "debug":
		level = slog.LevelDebug
	case "info":
		level = slog.LevelInfo
	case "warn", "warning":
		level = slog.LevelWarn
	case "error", "err":
		level = slog.LevelError
	default:
		level = slog.LevelInfo // 默认级别
	}

	var handler slog.Handler
	opts := &slog.HandlerOptions{
		Level:     level,
		AddSource: true, // 添加源文件和行号信息
	}

	var output io.Writer = os.Stdout // 默认输出到标准输出

	switch strings.ToLower(cfg.Format) {
	case "json":
		handler = slog.NewJSONHandler(output, opts)
	case "text":
		fallthrough
	default:
		handler = slog.NewTextHandler(output, opts)
	}

	globalLogger = slog.New(handler)
	slog.SetDefault(globalLogger) // 将其设置为标准库 slog 的默认 logger
}

// L 返回全局日志记录器实例。
// 如果尚未初始化，它会使用默认配置进行初始化。
func L(ctx context.Context) *slog.Logger {
	if globalLogger == nil {
		// 后备初始化，以防 InitGlobalLogger 未被显式调用
		// 在实际应用中，应确保在应用启动早期调用 InitGlobalLogger
		InitGlobalLogger(config.LogConfig{Level: "info", Format: "text"}) // 使用硬编码的默认值
		globalLogger.WarnContext(ctx, "Global logger accessed before explicit initialization. Using default settings.")
	}
	// 我们可以考虑从 context 中获取请求特定的 logger (如果未来需要追踪请求ID等)
	// For now, return the global logger.
	return globalLogger
}

// Convenience functions (optional, but can be nice)

func Debug(msg string, args ...any) {
	L(context.Background()).Debug(msg, args...)
}

func Info(msg string, args ...any) {
	L(context.Background()).Info(msg, args...)
}

func Warn(msg string, args ...any) {
	L(context.Background()).Warn(msg, args...)
}

func Error(msg string, args ...any) {
	L(context.Background()).Error(msg, args...)
}

func DebugContext(ctx context.Context, msg string, args ...any) {
	L(ctx).DebugContext(ctx, msg, args...)
}

func InfoContext(ctx context.Context, msg string, args ...any) {
	L(ctx).InfoContext(ctx, msg, args...)
}

func WarnContext(ctx context.Context, msg string, args ...any) {
	L(ctx).WarnContext(ctx, msg, args...)
}

func ErrorContext(ctx context.Context, msg string, args ...any) {
	L(ctx).ErrorContext(ctx, msg, args...)
}
