package admin

import (
	"context"
	"crypto/rand"
	"errors"
	"flag"
	"fmt"
	"math/big"
	"os"

	"github.com/Sakura-Byte/kikoeru-go/pkg/apperrors"
	"github.com/Sakura-Byte/kikoeru-go/pkg/auth"
	"github.com/Sakura-Byte/kikoeru-go/pkg/config"
	"github.com/Sakura-Byte/kikoeru-go/pkg/log"
	"github.com/Sakura-Byte/kikoeru-go/pkg/storage/database"
)

const (
	// RandomPasswordLength defines the length of generated random passwords
	RandomPasswordLength = 12
	// RandomPasswordChars defines the characters used in random password generation
	RandomPasswordChars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()-_=+"
)

// HandleCommands processes admin commands from the command line
// Returns true if a command was handled, false if normal server startup should proceed
func HandleCommands(ctx context.Context, args []string, configPath string) bool {
	if len(args) < 2 || args[1] != "admin" {
		return false
	}

	handleAdminCommand(ctx, args[2:], configPath)
	return true
}

// handleAdminCommand manages admin operations from the command line
func handleAdminCommand(ctx context.Context, args []string, configPath string) {
	// Define subcommands for admin operations
	randomCmd := flag.NewFlagSet("random", flag.ExitOnError)
	setCmd := flag.NewFlagSet("set", flag.ExitOnError)

	if len(args) < 1 {
		printUsage()
		os.Exit(1)
	}

	// Load the configuration
	appConfig, err := config.LoadConfig(configPath)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error loading application configuration: %v\n", err)
		os.Exit(1)
	}

	// Connect to the database
	log.InitGlobalLogger(appConfig.Log)
	db, err := database.ConnectDB(appConfig.Database, appConfig.Paths, log.With("module", "database"))
	if err != nil {
		log.Error(ctx, "Failed to connect to database", "error", err)
		os.Exit(1)
	}

	// Create the user repository
	userRepo := database.NewUserRepository(db)

	// Parse and handle subcommands
	switch args[0] {
	case "random":
		if err := randomCmd.Parse(args[1:]); err != nil {
			log.Error(ctx, "Failed to parse random command", "error", err)
			os.Exit(1)
		}
		if randomCmd.NArg() != 1 {
			log.Error(ctx, "Random command requires exactly one username argument")
			printUsage()
			os.Exit(1)
		}
		username := randomCmd.Arg(0)
		resetPasswordWithRandom(ctx, userRepo, username, appConfig.Auth.BcryptCost)

	case "set":
		if err := setCmd.Parse(args[1:]); err != nil {
			log.Error(ctx, "Failed to parse set command", "error", err)
			os.Exit(1)
		}
		if setCmd.NArg() != 2 {
			log.Error(ctx, "Set command requires exactly two arguments: password and username")
			printUsage()
			os.Exit(1)
		}
		password := setCmd.Arg(0)
		username := setCmd.Arg(1)
		resetPasswordWithSet(ctx, userRepo, username, password, appConfig.Auth.BcryptCost)

	default:
		printUsage()
		os.Exit(1)
	}
}

// PrintUsage prints the usage information for admin commands
func printUsage() {
	fmt.Println("Kikoeru Admin Command Usage:")
	fmt.Println("  kikoeru-server admin random USERNAME - Generate a random password for USERNAME")
	fmt.Println("  kikoeru-server admin set NEW_PASSWORD USERNAME - Set NEW_PASSWORD for USERNAME")
}

// resetPasswordWithRandom generates a random password for the specified user
func resetPasswordWithRandom(ctx context.Context, userRepo database.UserRepository, username string, bcryptCost int) {
	// Check if user exists
	user, err := userRepo.GetByUsername(ctx, username)
	if err != nil {
		if errors.Is(err, apperrors.ErrUserNotFound) {
			log.Error(ctx, "User not found", "username", username)
			os.Exit(1)
		}
		log.Error(ctx, "Failed to get user", "error", err)
		os.Exit(1)
	}

	// Generate a random password
	password := generateRandomPassword(RandomPasswordLength)

	// Hash the password
	hashedPassword, err := auth.HashPassword(password, bcryptCost)
	if err != nil {
		log.Error(ctx, "Failed to hash password", "error", err)
		os.Exit(1)
	}

	// Update the user with the new password
	user.Password = hashedPassword
	err = userRepo.Update(ctx, user)
	if err != nil {
		log.Error(ctx, "Failed to update user password", "error", err)
		os.Exit(1)
	}

	fmt.Println("************************************************************")
	fmt.Printf("Password for user '%s' has been reset.\n", username)
	fmt.Println("Generated random password is:")
	fmt.Println(password)
	fmt.Println("Please store this password securely.")
	fmt.Println("************************************************************")
}

// resetPasswordWithSet sets a specific password for the specified user
func resetPasswordWithSet(ctx context.Context, userRepo database.UserRepository, username, password string, bcryptCost int) {
	// Check if user exists
	user, err := userRepo.GetByUsername(ctx, username)
	if err != nil {
		if errors.Is(err, apperrors.ErrUserNotFound) {
			log.Error(ctx, "User not found", "username", username)
			os.Exit(1)
		}
		log.Error(ctx, "Failed to get user", "error", err)
		os.Exit(1)
	}

	// Hash the password
	hashedPassword, err := auth.HashPassword(password, bcryptCost)
	if err != nil {
		log.Error(ctx, "Failed to hash password", "error", err)
		os.Exit(1)
	}

	// Update the user with the new password
	user.Password = hashedPassword
	err = userRepo.Update(ctx, user)
	if err != nil {
		log.Error(ctx, "Failed to update user password", "error", err)
		os.Exit(1)
	}

	log.Info(ctx, "Password for user has been set to the specified value", "username", username)
}

// generateRandomPassword creates a cryptographically secure random password
func generateRandomPassword(length int) string {
	password := make([]byte, length)
	charsetLength := big.NewInt(int64(len(RandomPasswordChars)))

	for i := 0; i < length; i++ {
		randomIndex, err := rand.Int(rand.Reader, charsetLength)
		if err != nil {
			fmt.Fprintf(os.Stderr, "Failed to generate random number for password: %v\n", err)
			os.Exit(1)
		}
		password[i] = RandomPasswordChars[randomIndex.Int64()]
	}

	return string(password)
}
