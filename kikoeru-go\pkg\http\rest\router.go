package rest

import (
	"fmt"
	"log/slog"
	"net/http"
	"strings"

	"os"
	"path/filepath"

	"github.com/Sakura-Byte/kikoeru-go/pkg/config"
	"github.com/Sakura-Byte/kikoeru-go/pkg/http/middleware"
	"github.com/Sakura-Byte/kikoeru-go/pkg/http/rest/handler"
	"github.com/Sakura-Byte/kikoeru-go/pkg/ports"
	"github.com/Sakura-Byte/kikoeru-go/pkg/scanner"
	"github.com/gin-contrib/cors"
	"github.com/gin-contrib/gzip"
	"github.com/gin-gonic/gin"
)

func SetupRouter(
	appConfig *config.AppConfig,
	logger *slog.Logger,
	userService ports.UserService, // Changed to ports.UserService
	workService ports.WorkService,
	mediaService ports.MediaService,
	fileSystemService ports.FileSystemService,
	playHistoryService ports.PlayHistoryService,
	summaryService ports.SummaryService,
	scannerService *scanner.ScannerService,
	playlistService ports.PlaylistService,
	feedbackService ports.FeedbackService,
	storageAdminService ports.StorageAdminService,
	backgroundTaskService ports.BackgroundTaskService,
	reviewService ports.ReviewService,
	subtitleService ports.SubtitleService,
	archivePasswordService ports.ArchivePasswordService,
) *gin.Engine {
	router := gin.Default()

	if appConfig.Server.EnableGzip {
		router.Use(gzip.Gzip(gzip.DefaultCompression))
	}

	corsConfig := cors.DefaultConfig()
	corsConfig.AllowAllOrigins = true
	corsConfig.AllowMethods = []string{"GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"}
	corsConfig.AllowHeaders = []string{"Origin", "Content-Type", "Accept", "Authorization", "X-Requested-With"}
	corsConfig.AllowCredentials = true
	router.Use(cors.New(corsConfig))

	authMw := middleware.AuthMiddleware(appConfig)
	adminOnlyMw := middleware.AdminRequiredMiddleware()

	authHandler := handler.NewAuthHandler(userService, appConfig) // Changed to ports.UserService
	workHandler := handler.NewWorkHandler(workService, logger)
	mediaHandler := handler.NewMediaHandler(mediaService, logger.With("handler", "MediaHandler"))
	playlistHandler := handler.NewPlaylistHandler(playlistService)
	feedbackHandler := handler.NewFeedbackHandler(feedbackService)
	storageAdminHandler := handler.NewStorageAdminHandler(storageAdminService)
	playHistoryHandler := handler.NewPlayHistoryHandler(playHistoryService, workService, logger.With("handler", "PlayHistoryHandler"))
	scannerAdminHandler := handler.NewScannerAdminHandler(scannerService, logger.With("handler", "ScannerAdminHandler"))
	backgroundTaskHandler := handler.NewBackgroundTaskHandler(backgroundTaskService, logger.With("handler", "BackgroundTaskHandler"))
	reviewHandler := handler.NewReviewHandler(reviewService)
	userAdminHandler := handler.NewUserAdminHandler(userService) // Changed to ports.UserService
	fileSystemHandler := handler.NewFileSystemHandler(fileSystemService, logger.With("handler", "FileSystemHandler"))
	summaryHandler := handler.NewSummaryHandler(summaryService, logger)
	siteHandler := handler.NewSiteHandler(appConfig)
	archiveHandler := handler.NewArchiveHandler(fileSystemService, logger.With("handler", "ArchiveHandler"))
	subtitleHandler := handler.NewSubtitleHandler(subtitleService, logger.With("handler", "SubtitleHandler"))
	adminProfileHandler := handler.NewAdminProfileHandler(userService)

	// New admin handlers for system, cache, and data management
	adminSystemHandler := handler.NewAdminSystemHandler(appConfig, userService, workService, storageAdminService)
	adminCacheHandler := handler.NewAdminCacheHandler(appConfig, logger.With("handler", "AdminCacheHandler"))
	adminDataHandler := handler.NewAdminDataHandler(appConfig, logger.With("handler", "AdminDataHandler"), userService, workService)
	adminArchivePasswordHandler := handler.NewAdminArchivePasswordHandler(archivePasswordService, logger.With("handler", "AdminArchivePasswordHandler"))

	apiV1 := router.Group("/api/v1")
	{
		// Site information endpoint (public)
		siteRoutes := apiV1.Group("/site")
		{
			siteRoutes.GET("/info", siteHandler.GetSiteInfo)
		}

		authRoutes := apiV1.Group("/auth")
		{
			authRoutes.POST("/register", authHandler.Register)
			authRoutes.POST("/login", authHandler.Login)
			authRoutes.POST("/email/request-password-reset", authHandler.RequestPasswordResetHandler)
			authRoutes.POST("/email/reset-password", authHandler.ResetPasswordWithTokenHandler)
			authRoutes.POST("/email/verify", authHandler.VerifyEmailHandler)
			authRoutes.GET("/me", authMw, authHandler.GetMe)
		}
		worksRoutes := apiV1.Group("/works")
		{
			worksRoutes.GET("", workHandler.ListWorks)

		}
		workRoutes := apiV1.Group("/work")
		{
			workRoutes.GET("/:originalID/info", workHandler.GetWorkInfo)
		}

		// 添加独立的搜索端点
		searchRoutes := apiV1.Group("/search")
		{
			searchRoutes.GET("/:query", workHandler.SearchWorks)
		}

		apiV1.GET("/circles", summaryHandler.ListCircles)
		apiV1.GET("/tags", summaryHandler.ListTags)
		apiV1.GET("/vas", summaryHandler.ListVAs)

		meRoutes := apiV1.Group("/me")
		meRoutes.Use(authMw)
		{
			meRoutes.POST("/password/change", authHandler.ChangePassword)
			meRoutes.POST("/email/link", authHandler.LinkEmailHandler)
			meRoutes.POST("/email/change", authHandler.ChangeEmailHandler)
			meRoutes.POST("/email/unlink", authHandler.UnlinkEmailHandler)
			mePlaylistRoutes := meRoutes.Group("/playlists")
			{
				mePlaylistRoutes.POST("", playlistHandler.CreatePlaylist)
				mePlaylistRoutes.GET("", playlistHandler.ListUserPlaylists)
			}
			meHistoryRoutes := meRoutes.Group("/history")
			{
				meHistoryRoutes.POST("", playHistoryHandler.RecordPlayPosition)
				meHistoryRoutes.GET("", playHistoryHandler.GetUserPlayHistory)
				meHistoryRoutes.DELETE("/:historyID", playHistoryHandler.DeletePlayHistoryEntry)
			}
		}

		// Review routes
		reviewRoutes := apiV1.Group("/review")
		reviewRoutes.Use(authMw)
		{
			reviewRoutes.PUT("", reviewHandler.PutReview)
			reviewRoutes.GET("", reviewHandler.GetReviews)
			reviewRoutes.GET("/work/:originalID", reviewHandler.GetUserReviewForWork)
			reviewRoutes.DELETE("/:originalID", reviewHandler.DeleteReview)
		}

		playlistSpecificRoutes := apiV1.Group("/playlists")
		playlistSpecificRoutes.Use(authMw)
		{
			playlistSpecificRoutes.GET("/:playlistID", playlistHandler.GetPlaylistDetails)
			playlistSpecificRoutes.PUT("/:playlistID", playlistHandler.UpdatePlaylistInfo)
			playlistSpecificRoutes.DELETE("/:playlistID", playlistHandler.DeletePlaylist)
			playlistSpecificRoutes.POST("/:playlistID/items", playlistHandler.AddItemToPlaylist)
			playlistSpecificRoutes.DELETE("/:playlistID/items/:itemID", playlistHandler.RemoveItemFromPlaylist)
		}
		mediaRoutes := apiV1.Group("/media")
		mediaRoutes.Use(authMw)
		{
			mediaRoutes.GET("/stream/work/:workID/track/*trackPath", mediaHandler.GetMediaStream)
		}

		apiV1.GET("/cover/:originalID", mediaHandler.ServeCoverImageByOriginalID)

		feedbackPublicRoutes := apiV1.Group("/feedback")
		{
			feedbackPublicRoutes.POST("", feedbackHandler.SubmitFeedback)
		}
		queueRoutes := apiV1.Group("/queue")
		{
			queueRoutes.GET("/random", workHandler.GetRandomQueue)
		}

		fsRoutes := apiV1.Group("/fs")
		fsRoutes.Use(authMw)
		{
			fsRoutes.POST("/list", fileSystemHandler.ListFilesOrDirectories)
			fsRoutes.POST("/tree", fileSystemHandler.GetFileSystemTree)
			fsRoutes.GET("/link", fileSystemHandler.GetFileLink)
		}
		apiV1.GET("/proxy/:storage_id/:encoded_path", fileSystemHandler.ProxyFileStream)

		// Archive routes
		archiveRoutes := apiV1.Group("/archive")
		archiveRoutes.Use(authMw)
		{
			archiveRoutes.POST("/list", archiveHandler.ListArchiveContents)
			archiveRoutes.POST("/tree", archiveHandler.TreeArchiveContents)
			archiveRoutes.GET("/extract", archiveHandler.ExtractArchiveFile)
			// For streaming files from archives with URL path parameters
			apiV1.GET("/archive/proxy/:storage_id/:encoded_archive_path/:encoded_file_path", archiveHandler.ProxyArchiveFileStream)
		}

		// Subtitle routes
		subtitleRoutes := apiV1.Group("/subtitles")
		subtitleRoutes.Use(authMw)
		{
			subtitleRoutes.POST("", subtitleHandler.UploadSubtitle)
			subtitleRoutes.GET("/:id/content", subtitleHandler.GetSubtitleContent)
			subtitleRoutes.GET("/find", subtitleHandler.FindSubtitlesForTrack)
			subtitleRoutes.POST("/vote", subtitleHandler.VoteOnSubtitle)
			subtitleRoutes.DELETE("/:id", subtitleHandler.DeleteSubtitle)
		}

		adminRoutes := apiV1.Group("/admin")
		adminRoutes.Use(authMw)
		adminRoutes.Use(adminOnlyMw)
		{
			adminRoutes.POST("/profile/update", adminProfileHandler.UpdateAdminProfile)

			// Admin system configuration
			adminRoutes.PUT("/config", adminSystemHandler.UpdateSystemConfig)
			adminRoutes.GET("/system/info", adminSystemHandler.GetSystemInfo)

			// Admin cache management
			adminCacheRoutes := adminRoutes.Group("/cache")
			{
				adminCacheRoutes.GET("/info", adminCacheHandler.GetCacheInfo)
				adminCacheRoutes.POST("/clear-cover", adminCacheHandler.ClearCoverCache)
				adminCacheRoutes.POST("/clear-file", adminCacheHandler.ClearFileCache)
				adminCacheRoutes.POST("/clear-all", adminCacheHandler.ClearAllCache)
			}

			// Admin data management
			adminDataRoutes := adminRoutes.Group("/data")
			{
				adminDataRoutes.GET("/export/works", adminDataHandler.ExportWorks)
				adminDataRoutes.GET("/export/users", adminDataHandler.ExportUsers)
				adminDataRoutes.POST("/import", adminDataHandler.ImportData)
			}

			adminUserRoutes := adminRoutes.Group("/users")
			{
				adminUserRoutes.GET("", userAdminHandler.ListUsers)
				adminUserRoutes.GET("/:userID", userAdminHandler.GetUserByIDByAdmin)
				adminUserRoutes.PUT("/:userID", userAdminHandler.UpdateUserByAdmin)
				adminUserRoutes.DELETE("/:userID", userAdminHandler.DeleteUserByAdmin)
			}

			adminFeedbackRoutes := adminRoutes.Group("/feedback")
			{
				adminFeedbackRoutes.GET("", feedbackHandler.ListFeedback)
				adminFeedbackRoutes.GET("/:feedbackID", feedbackHandler.GetFeedback)
				adminFeedbackRoutes.PUT("/:feedbackID", feedbackHandler.UpdateFeedback)
			}
			adminStorageRoutes := adminRoutes.Group("/storages")
			{
				adminStorageRoutes.GET("/driver-definitions", storageAdminHandler.GetDriverDefinitionsHandler)
				adminStorageRoutes.GET("", storageAdminHandler.ListStorageSources)
				adminStorageRoutes.POST("", storageAdminHandler.CreateStorageSource)
				adminStorageRoutes.GET("/:id", storageAdminHandler.GetStorageSource)
				adminStorageRoutes.PUT("/:id", storageAdminHandler.UpdateStorageSource)
				adminStorageRoutes.DELETE("/:id", storageAdminHandler.DeleteStorageSource)
				adminStorageRoutes.POST("/reload-drivers", storageAdminHandler.ReloadAllDriversHandler)
				adminStorageRoutes.POST("/test-connection", storageAdminHandler.TestStorageConnection)
			}
			adminWorkRoutes := adminRoutes.Group("/works")
			{
				adminWorkRoutes.PUT("/:workID", workHandler.UpdateWorkByAdmin)
				adminWorkRoutes.DELETE("/:workID", workHandler.DeleteWorkByAdmin)
				workMetaRoutes := adminWorkRoutes.Group("/:workID")
				{
					workMetaRoutes.POST("/tags", workHandler.AddTagToWork)
					workMetaRoutes.DELETE("/tags/:tagName", workHandler.RemoveTagFromWork)
					workMetaRoutes.PUT("/tags", workHandler.ReplaceWorkTags)
					workMetaRoutes.POST("/vas", workHandler.AddVAToWork)
					workMetaRoutes.DELETE("/vas/:vaName", workHandler.RemoveVAFromWork)
					workMetaRoutes.PUT("/vas", workHandler.ReplaceWorkVAs)
					workMetaRoutes.POST("/scrape", workHandler.TriggerScrapeForWorkByAdmin)
				}
				adminWorkRoutes.POST("/scrape-all", workHandler.TriggerScrapeForAllWorksByAdmin)
			}
			adminScannerRoutes := adminRoutes.Group("/scanner")
			{
				adminScannerRoutes.POST("/scan/library/:libraryName", scannerAdminHandler.TriggerLibraryScan)
				adminScannerRoutes.GET("/status", scannerAdminHandler.GetScannerStatus)
			}
			adminTaskRoutes := adminRoutes.Group("/tasks")
			{
				adminTaskRoutes.POST("/scan-library", backgroundTaskHandler.SubmitScanLibraryTask)
				adminTaskRoutes.POST("/scrape-all-works", backgroundTaskHandler.SubmitScrapeAllWorksTask)
				adminTaskRoutes.POST("/scrape-single-work", backgroundTaskHandler.SubmitScrapeSingleWorkTask)
				adminTaskRoutes.GET("", backgroundTaskHandler.ListTasks)
				adminTaskRoutes.GET("/:taskID", backgroundTaskHandler.GetTaskStatusByID)
				adminTaskRoutes.POST("/:taskID/cancel", backgroundTaskHandler.CancelTask)
			}
			adminSubtitleRoutes := adminRoutes.Group("/subtitles")
			{
				adminSubtitleRoutes.GET("", subtitleHandler.GetAllSubtitles)
				adminSubtitleRoutes.PUT("/:id/visibility", subtitleHandler.UpdateSubtitleVisibility)
			}

			// Archive password management routes
			adminArchivePasswordRoutes := adminRoutes.Group("/archive-passwords")
			{
				adminArchivePasswordRoutes.GET("", adminArchivePasswordHandler.GetAllPasswords)
				adminArchivePasswordRoutes.POST("", adminArchivePasswordHandler.CreatePassword)
				adminArchivePasswordRoutes.PUT("/:id", adminArchivePasswordHandler.UpdatePassword)
				adminArchivePasswordRoutes.DELETE("/:id", adminArchivePasswordHandler.DeletePassword)
				adminArchivePasswordRoutes.POST("/batch-import", adminArchivePasswordHandler.BatchImportPasswords)
				adminArchivePasswordRoutes.GET("/cache", adminArchivePasswordHandler.GetAllCachedPasswords)
				adminArchivePasswordRoutes.DELETE("/cache", adminArchivePasswordHandler.DeleteCachedPassword)
			}
		}
	}
	router.GET("/health", func(c *gin.Context) { c.JSON(http.StatusOK, gin.H{"status": "ok"}) })

	if logger != nil {
		logger.Debug("Registered routes:")
		for _, routeInfo := range router.Routes() {
			logger.Debug("Route", "method", routeInfo.Method, "path", routeInfo.Path, "handler", routeInfo.Handler)
		}
	} else {
		fmt.Println("[DEBUG] Registered routes:")
		for _, routeInfo := range router.Routes() {
			fmt.Printf("[DEBUG] Method: %s, Path: %s, Handler: %s\n", routeInfo.Method, routeInfo.Path, routeInfo.Handler)
		}
	}

	frontendDistPath := "./dist"
	if _, err := os.Stat(frontendDistPath); !os.IsNotExist(err) {
		logger.Info("Serving frontend static files from", "path", frontendDistPath)
		router.StaticFS("/assets", http.Dir(filepath.Join(frontendDistPath, "assets")))
		router.StaticFS("/css", http.Dir(filepath.Join(frontendDistPath, "css")))
		router.StaticFS("/js", http.Dir(filepath.Join(frontendDistPath, "js")))
		router.StaticFS("/img", http.Dir(filepath.Join(frontendDistPath, "img")))
		router.StaticFile("/favicon.ico", filepath.Join(frontendDistPath, "favicon.ico"))

		router.NoRoute(func(c *gin.Context) {
			if strings.HasPrefix(c.Request.URL.Path, "/api/") ||
				c.Request.URL.Path == "/health" {
				return
			}
			indexPath := filepath.Join(frontendDistPath, "index.html")
			if _, err := os.Stat(indexPath); os.IsNotExist(err) {
				logger.Error("SPA index.html not found at expected path", "path", indexPath)
				c.String(http.StatusNotFound, "SPA index.html not found")
			} else {
				c.File(indexPath)
			}
		})
	} else {
		logger.Warn("Frontend distribution path not found, SPA fallback not configured.", "path_checked", frontendDistPath)
	}
	return router
}
