package driver

import (
	"context"
	"fmt"
	"log/slog"
	"path/filepath"
	"reflect"
	"strings"
	"time"

	"github.com/Xhofe/go-cache"
)

// Global cache for path-to-ID resolutions across all storage instances
var pathIDCache = cache.NewMemCache(cache.WithShards[string](1024))

// PathResolver implements the IDResolver interface
type PathResolver struct{}

// ResolvePath resolves a path to an ID for ID-based drivers
// For path-based drivers, this simply returns the cleaned path
func (r *PathResolver) ResolvePath(ctx context.Context, driver StorageDriver, path string) (string, error) {
	// Generate cache key that includes storage ID to avoid conflicts
	storageID := driver.GetStorage().ID
	cacheKey := fmt.Sprintf("pathToID:sid%d:%s", storageID, path)

	storage := driver.GetStorage()
	cacheExpiration := time.Duration(storage.CacheExpiration) * time.Minute
	if storage.CacheExpiration <= 0 {
		cacheExpiration = 5 * time.Minute
	}

	// Check if we have a cached resolution
	if cachedID, ok := pathIDCache.Get(cacheKey); ok {
		slog.Debug("PathResolver: Cache hit for path resolution",
			"path", path,
			"storage_id", storageID,
			"cached_id", cachedID)
		return cachedID, nil
	}

	// Try to use directory tree model if available
	slog.Debug("PathResolver: Attempting to use directory model for path resolution",
		"path", path,
		"storage_id", storageID)

	// Get directory model for this storage
	dirModel := GetDirectoryTreeModel(ctx, storageID, driver, slog.Default().With("module", "PathResolver"))
	if dirModel != nil {
		resolvedID, err := dirModel.ResolvePathToID(ctx, path)
		if err == nil {
			// Cache the result
			pathIDCache.Set(cacheKey, resolvedID, cache.WithEx[string](cacheExpiration))

			slog.Debug("PathResolver: Resolved path using directory model",
				"path", path,
				"resolved_id", resolvedID)
			return resolvedID, nil
		}

		// If there was an error with directory model, fall back to standard resolution
		slog.Debug("PathResolver: Directory model resolution failed, using standard resolution",
			"path", path,
			"error", err)
	}

	// Try a direct Get call if driver implements Getter
	// This can be more efficient than traversing the path for some storage providers
	if getter, ok := driver.(Getter); ok {
		// Skip path traversal and try direct Get if the path might be an ID already
		if !strings.Contains(path, "/") || path == "/" {
			obj, err := getter.Get(ctx, path)
			if err == nil {
				id := obj.GetID()
				slog.Debug("PathResolver: Direct Get succeeded",
					"path", path,
					"id", id)

				// Cache the result
				pathIDCache.Set(cacheKey, id, cache.WithEx[string](cacheExpiration))
				return id, nil
			}
		} else {
			// For paths that are clearly not IDs, we might still try Get
			// if the driver supports it, but only if it's not a very long path
			// that would likely fail (to avoid unnecessary API calls)
			pathParts := strings.Split(path, "/")
			if len(pathParts) <= 3 { // Only try for paths with few components
				obj, err := getter.Get(ctx, path)
				if err == nil {
					id := obj.GetID()
					slog.Debug("PathResolver: Direct Get succeeded for path",
						"path", path,
						"id", id)

					// Cache the result
					pathIDCache.Set(cacheKey, id, cache.WithEx[string](cacheExpiration))
					return id, nil
				}
			}
		}
	}

	// Add debug logging to see driver type information
	driverType := reflect.TypeOf(driver)
	slog.Debug("PathResolver: Driver type info",
		"driver_type", driverType.String(),
		"driver_kind", driverType.Kind().String(),
		"driver_ptr_elem", reflect.TypeOf(driver).Elem().String(),
		"input_path", path)

	// Check if the driver directly implements the interfaces
	isRootIDGetterDirect := reflect.TypeOf(driver).Implements(reflect.TypeOf((*IRootIDGetter)(nil)).Elem())
	isRootPathGetterDirect := reflect.TypeOf(driver).Implements(reflect.TypeOf((*IRootPathGetter)(nil)).Elem())

	slog.Debug("PathResolver: Interface checks",
		"implements_IRootIDGetter_directly", isRootIDGetterDirect,
		"implements_IRootPathGetter_directly", isRootPathGetterDirect)

	// Check if this is a path-based driver
	detector := DriverTypeDetector{}
	isPathBased := detector.IsPathBased(driver)
	isIDBased := detector.IsIDBased(driver)

	slog.Debug("PathResolver: Driver type detection",
		"is_path_based", isPathBased,
		"is_id_based", isIDBased)

	// Only use path-based handling if it's a path-based driver and NOT also an ID-based driver
	// This handles the case where a driver implements both interfaces but should be treated as ID-based
	if isPathBased && !isIDBased {
		// For path-based drivers, just return the cleaned path
		cleanPath := filepath.Clean(path)
		// Convert backslashes to forward slashes for consistency
		cleanPath = filepath.ToSlash(cleanPath)
		if cleanPath == "." {
			cleanPath = "/"
		}
		rootPathGetter, ok := driver.(IRootPathGetter)
		if ok {
			slog.Debug("PathResolver: Using path-based driver",
				"root_path", rootPathGetter.GetRootPath())
		}
		slog.Debug("PathResolver: Resolved path", "original", path, "resolved", cleanPath)
		return cleanPath, nil
	}

	// We expect this to be an ID-based driver with a root ID
	rootIDGetter, ok := driver.(IRootIDGetter)
	if !ok {
		// Try to find the actual concrete type to check why interface is not detected
		actualType := reflect.ValueOf(driver).Type().String()
		actualElemType := ""
		if reflect.ValueOf(driver).Kind() == reflect.Ptr {
			actualElemType = reflect.ValueOf(driver).Elem().Type().String()
		}

		slog.Error("PathResolver: Driver interface check failed",
			"actual_type", actualType,
			"actual_elem_type", actualElemType,
			"implements_IRootIDGetter", reflect.TypeOf(driver).Implements(reflect.TypeOf((*IRootIDGetter)(nil)).Elem()),
			"implements_IRootPathGetter", reflect.TypeOf(driver).Implements(reflect.TypeOf((*IRootPathGetter)(nil)).Elem()))

		return "", fmt.Errorf("driver neither implements IRootPathGetter nor IRootIDGetter")
	}

	// Root path is a special case - return the root folder ID
	if path == "/" || path == "." || path == "" {
		rootID := rootIDGetter.GetRootID()
		slog.Debug("PathResolver: Resolved root path to root ID", "root_id", rootID)

		// Cache the root ID resolution
		pathIDCache.Set(cacheKey, rootID, cache.WithEx[string](cacheExpiration))

		return rootID, nil
	}

	// Clean the path and ensure it starts with a /
	// Use forward slashes for path traversal regardless of platform
	path = filepath.ToSlash(filepath.Clean(path))
	if !strings.HasPrefix(path, "/") {
		path = "/" + path
	}

	// Split the path into components
	parts := strings.Split(path, "/")
	// Remove empty parts (such as the one before the first slash)
	var cleanParts []string
	for _, part := range parts {
		if part != "" {
			cleanParts = append(cleanParts, part)
		}
	}

	// Start from the root folder ID
	currentID := rootIDGetter.GetRootID()
	currentPathForLogging := "/"

	slog.Debug("PathResolver: Starting path traversal",
		"root_id", currentID,
		"path_parts", cleanParts)

	// Check if we can skip some intermediate calls by looking at parent paths
	// This is particularly useful for deep paths
	if len(cleanParts) > 2 {
		// Try to resolve from a parent path that might be cached
		for i := len(cleanParts) - 1; i > 0; i-- {
			parentPath := "/" + strings.Join(cleanParts[:i], "/")
			parentCacheKey := fmt.Sprintf("pathToID:sid%d:%s", storageID, parentPath)

			if cachedParentID, ok := pathIDCache.Get(parentCacheKey); ok {
				slog.Debug("PathResolver: Using cached parent path",
					"parent_path", parentPath,
					"cached_id", cachedParentID)

				// Start traversal from this parent ID instead of root
				currentID = cachedParentID
				currentPathForLogging = parentPath
				cleanParts = cleanParts[i:] // Keep only the remaining path parts
				break
			}
		}
	}

	// Traverse the path parts to find the final ID
	for _, part := range cleanParts {
		found := false

		// Build the partial path for potential caching of intermediate results
		partialPath := currentPathForLogging
		if partialPath == "/" {
			partialPath += part
		} else {
			partialPath += "/" + part
		}
		partialCacheKey := fmt.Sprintf("pathToID:sid%d:%s", storageID, partialPath)

		// Check if we have this path segment cached
		if cachedID, ok := pathIDCache.Get(partialCacheKey); ok {
			currentID = cachedID
			currentPathForLogging = partialPath
			slog.Debug("PathResolver: Using cached ID for path segment",
				"path_segment", partialPath,
				"cached_id", cachedID)
			found = true
			continue
		}

		// List contents of the current directory using the driver
		obj := NewIdAlistObj(currentID, currentPathForLogging)
		items, err := driver.List(ctx, obj, AlistListArgs{})
		if err != nil {
			slog.Error("PathResolver: Failed to list directory during path traversal",
				"current_id", currentID,
				"current_path", currentPathForLogging,
				"error", err)
			return "", fmt.Errorf("failed to resolve path '%s': error listing '%s': %w", path, currentPathForLogging, err)
		}

		slog.Debug("PathResolver: Searching for path component",
			"current_id", currentID,
			"path_component", part,
			"items_found", len(items))

		// Find the next path part in the list
		for _, item := range items {
			if item.GetName() == part {
				currentID = item.GetID()
				currentPathForLogging = partialPath
				found = true

				// Cache this path segment
				pathIDCache.Set(partialCacheKey, currentID, cache.WithEx[string](cacheExpiration))

				slog.Debug("PathResolver: Found path component",
					"component", part,
					"resolved_id", currentID,
					"partial_path", partialPath)
				break
			}
		}

		if !found {
			slog.Error("PathResolver: Path component not found",
				"component", part,
				"path", path,
				"current_path", currentPathForLogging)
			return "", ErrFileNotFound
		}
	}

	slog.Debug("PathResolver: Successfully resolved path to ID",
		"path", path,
		"resolved_id", currentID)

	// Cache the final path resolution
	pathIDCache.Set(cacheKey, currentID, cache.WithEx[string](cacheExpiration))

	return currentID, nil
}

// NewIdAlistObj creates a new AlistObject with the given ID and path
func NewIdAlistObj(id string, path string) AlistObj {
	return &AlistObject{
		ID:       id,
		Path:     path,
		IsFolder: true, // Assume it's a folder when using it for listing
	}
}
