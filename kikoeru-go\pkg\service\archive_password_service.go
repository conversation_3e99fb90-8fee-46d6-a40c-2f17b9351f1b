package service

import (
	"context"
	"fmt"
	"log/slog"
	"strings"
	"sync"
	"time"

	"github.com/Sakura-Byte/kikoeru-go/pkg/apperrors"
	"github.com/Sakura-Byte/kikoeru-go/pkg/log"
	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
	"github.com/Sakura-Byte/kikoeru-go/pkg/storage/database"
)

// ArchivePasswordService handles archive password management
type ArchivePasswordService struct {
	repo   database.ArchivePasswordRepository
	logger *slog.Logger

	// Memory caches
	passwordList       []string
	passwordCache      map[string]string // key: storageID:path
	passwordListMutex  sync.RWMutex
	passwordCacheMutex sync.RWMutex

	// Cache refresh
	lastRefresh     time.Time
	refreshInterval time.Duration
}

// NewArchivePasswordService creates a new archive password service
func NewArchivePasswordService(repo database.ArchivePasswordRepository, logger *slog.Logger) *ArchivePasswordService {
	service := &ArchivePasswordService{
		repo:            repo,
		logger:          logger,
		passwordCache:   make(map[string]string),
		refreshInterval: 5 * time.Minute,
	}

	// Initialize the password list cache
	ctx := context.Background()
	if passwordList, err := service.loadPasswordListFromDB(ctx); err == nil {
		service.passwordList = passwordList
		service.lastRefresh = time.Now()
		log.Debug("Loaded %d passwords into memory cache\n", len(passwordList))
	} else {
		logger.ErrorContext(ctx, "Failed to load password list during initialization", "error", err)
	}

	// Load password cache
	service.preloadPasswordCache(ctx)

	return service
}

// preloadPasswordCache loads all cached passwords from the database
func (s *ArchivePasswordService) preloadPasswordCache(ctx context.Context) {
	caches, err := s.repo.GetAllCachedPasswords(ctx)
	if err != nil {
		s.logger.ErrorContext(ctx, "Failed to preload password cache", "error", err)
		return
	}

	s.passwordCacheMutex.Lock()
	defer s.passwordCacheMutex.Unlock()

	for _, cache := range caches {
		key := fmt.Sprintf("%d:%s", cache.StorageID, cache.ArchivePath)
		s.passwordCache[key] = cache.Password
	}

	log.Debug("Preloaded cached passwords into memory", "count", len(caches))
}

// loadPasswordListFromDB loads all passwords from the database
func (s *ArchivePasswordService) loadPasswordListFromDB(ctx context.Context) ([]string, error) {
	passwords, err := s.repo.GetAllPasswords(ctx)
	if err != nil {
		return nil, err
	}

	passwordList := make([]string, len(passwords))
	for i, p := range passwords {
		passwordList[i] = p.Password
	}

	return passwordList, nil
}

// checkAndRefreshPasswordList refreshes the password list if it's stale
func (s *ArchivePasswordService) checkAndRefreshPasswordList(ctx context.Context) {
	if time.Since(s.lastRefresh) < s.refreshInterval {
		return
	}

	// Do a non-blocking check if we need to refresh
	if !s.passwordListMutex.TryRLock() {
		return // Someone else is updating, skip this time
	}
	needsRefresh := time.Since(s.lastRefresh) >= s.refreshInterval
	s.passwordListMutex.RUnlock()

	if !needsRefresh {
		return
	}

	// Try to get a write lock for update
	if !s.passwordListMutex.TryLock() {
		return // Someone else is updating, skip this time
	}
	defer s.passwordListMutex.Unlock()

	// Double-check if we still need to refresh
	if time.Since(s.lastRefresh) < s.refreshInterval {
		return
	}

	// Load fresh passwords
	passwordList, err := s.loadPasswordListFromDB(ctx)
	if err != nil {
		s.logger.ErrorContext(ctx, "Failed to refresh password list", "error", err)
		return
	}

	s.passwordList = passwordList
	s.lastRefresh = time.Now()
	log.Debug("Refreshed password list cache", "count", len(passwordList))
}

// GetAllPasswords retrieves all passwords in the list
func (s *ArchivePasswordService) GetAllPasswords(ctx context.Context) ([]*models.ArchivePasswordResponse, error) {
	passwords, err := s.repo.GetAllPasswords(ctx)
	if err != nil {
		s.logger.ErrorContext(ctx, "Failed to get all passwords", "error", err)
		return nil, err
	}

	responses := make([]*models.ArchivePasswordResponse, len(passwords))
	for i, p := range passwords {
		responses[i] = &models.ArchivePasswordResponse{
			ID:          p.ID,
			Password:    p.Password,
			Description: p.Description,
			CreatedAt:   p.CreatedAt,
			UpdatedAt:   p.UpdatedAt,
		}
	}

	return responses, nil
}

// CreatePassword creates a new password in the list
func (s *ArchivePasswordService) CreatePassword(ctx context.Context, req *models.ArchivePasswordInput) (*models.ArchivePasswordResponse, error) {
	// Validate input
	if strings.TrimSpace(req.Password) == "" {
		return nil, apperrors.NewValidationError("password cannot be empty")
	}

	password := &models.ArchivePassword{
		Password:    strings.TrimSpace(req.Password),
		Description: strings.TrimSpace(req.Description),
	}

	if err := s.repo.CreatePassword(ctx, password); err != nil {
		s.logger.ErrorContext(ctx, "Failed to create password", "error", err)
		return nil, err
	}

	// Update the in-memory cache
	s.passwordListMutex.Lock()
	s.passwordList = append(s.passwordList, password.Password)
	s.passwordListMutex.Unlock()

	return &models.ArchivePasswordResponse{
		ID:          password.ID,
		Password:    password.Password,
		Description: password.Description,
		CreatedAt:   password.CreatedAt,
		UpdatedAt:   password.UpdatedAt,
	}, nil
}

// UpdatePassword updates an existing password
func (s *ArchivePasswordService) UpdatePassword(ctx context.Context, id uint, req *models.ArchivePasswordUpdateRequest) (*models.ArchivePasswordResponse, error) {
	// Validate input
	if strings.TrimSpace(req.Password) == "" {
		return nil, apperrors.NewValidationError("password cannot be empty")
	}

	// Get the old password first for cache update
	oldPassword, err := s.repo.GetPasswordByID(ctx, id)
	if err != nil {
		s.logger.ErrorContext(ctx, "Failed to get existing password", "id", id, "error", err)
		return nil, err
	}

	password := &models.ArchivePassword{
		Password:    strings.TrimSpace(req.Password),
		Description: strings.TrimSpace(req.Description),
	}

	if err := s.repo.UpdatePassword(ctx, id, password); err != nil {
		s.logger.ErrorContext(ctx, "Failed to update password", "id", id, "error", err)
		return nil, err
	}

	// Update the in-memory cache
	s.passwordListMutex.Lock()
	for i, p := range s.passwordList {
		if p == oldPassword.Password {
			s.passwordList[i] = password.Password
			break
		}
	}
	s.passwordListMutex.Unlock()

	// Get the updated password to return
	updatedPassword, err := s.repo.GetPasswordByID(ctx, id)
	if err != nil {
		s.logger.ErrorContext(ctx, "Failed to get updated password", "id", id, "error", err)
		return nil, err
	}

	return &models.ArchivePasswordResponse{
		ID:          updatedPassword.ID,
		Password:    updatedPassword.Password,
		Description: updatedPassword.Description,
		CreatedAt:   updatedPassword.CreatedAt,
		UpdatedAt:   updatedPassword.UpdatedAt,
	}, nil
}

// DeletePassword deletes a password from the list
func (s *ArchivePasswordService) DeletePassword(ctx context.Context, id uint) error {
	// Get the password to delete for cache update
	oldPassword, err := s.repo.GetPasswordByID(ctx, id)
	if err != nil {
		s.logger.ErrorContext(ctx, "Failed to get password to delete", "id", id, "error", err)
		return err
	}

	if err := s.repo.DeletePassword(ctx, id); err != nil {
		s.logger.ErrorContext(ctx, "Failed to delete password", "id", id, "error", err)
		return err
	}

	// Update the in-memory cache
	s.passwordListMutex.Lock()
	for i, p := range s.passwordList {
		if p == oldPassword.Password {
			// Remove by replacing with the last element and shrinking
			s.passwordList[i] = s.passwordList[len(s.passwordList)-1]
			s.passwordList = s.passwordList[:len(s.passwordList)-1]
			break
		}
	}
	s.passwordListMutex.Unlock()

	return nil
}

// BatchImportPasswords imports passwords from text with deduplication
func (s *ArchivePasswordService) BatchImportPasswords(ctx context.Context, req *models.ArchivePasswordBatchImportRequest) (*models.ArchivePasswordBatchImportResponse, error) {
	separator := req.Separator
	if separator == "" {
		separator = "\n"
	}

	// Split the text by separator
	lines := strings.Split(req.PasswordText, separator)

	response := &models.ArchivePasswordBatchImportResponse{
		Total: len(lines),
	}

	var validPasswords []*models.ArchivePassword
	var errors []string

	for i, line := range lines {
		password := strings.TrimSpace(line)
		if password == "" {
			response.Invalid++
			continue
		}

		// Check if password already exists
		exists, err := s.repo.PasswordExists(ctx, password)
		if err != nil {
			s.logger.ErrorContext(ctx, "Failed to check password existence", "password", password, "error", err)
			errors = append(errors, fmt.Sprintf("Line %d: failed to check existence", i+1))
			response.Invalid++
			continue
		}

		if exists {
			response.Duplicates++
			continue
		}

		validPasswords = append(validPasswords, &models.ArchivePassword{
			Password: password,
		})
	}

	// Batch insert valid passwords
	if len(validPasswords) > 0 {
		added, err := s.repo.CreatePasswordsInBatch(ctx, validPasswords)
		if err != nil {
			s.logger.ErrorContext(ctx, "Failed to batch create passwords", "count", len(validPasswords), "error", err)
			return nil, err
		}
		response.Added = added

		// Update the in-memory cache with new passwords
		s.passwordListMutex.Lock()
		for _, p := range validPasswords {
			s.passwordList = append(s.passwordList, p.Password)
		}
		s.passwordListMutex.Unlock()
	}

	response.Errors = errors
	return response, nil
}

// GetPasswordList returns all passwords for archive operations (without sensitive info in logs)
func (s *ArchivePasswordService) GetPasswordList(ctx context.Context) ([]string, error) {
	// Check if we need to refresh the cache
	s.checkAndRefreshPasswordList(ctx)

	// Return the cached password list
	s.passwordListMutex.RLock()
	defer s.passwordListMutex.RUnlock()

	// Make a copy to avoid concurrent modification
	result := make([]string, len(s.passwordList))
	copy(result, s.passwordList)

	return result, nil
}

// getCacheKey generates a unique key for the password cache
func getCacheKey(storageID uint, archivePath string) string {
	return fmt.Sprintf("%d:%s", storageID, archivePath)
}

// GetCachedPassword retrieves a cached password for a specific archive
func (s *ArchivePasswordService) GetCachedPassword(ctx context.Context, storageID uint, archivePath string) (string, error) {
	// First check in-memory cache
	cacheKey := getCacheKey(storageID, archivePath)

	s.passwordCacheMutex.RLock()
	if password, found := s.passwordCache[cacheKey]; found {
		s.passwordCacheMutex.RUnlock()

		// Asynchronously update last used timestamp in DB, don't wait for it
		go func() {
			timeoutCtx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
			defer cancel()
			_ = s.repo.UpdateCachedPasswordUsage(timeoutCtx, storageID, archivePath)
		}()

		return password, nil
	}
	s.passwordCacheMutex.RUnlock()

	// If not in memory cache, try database
	cache, err := s.repo.GetCachedPassword(ctx, storageID, archivePath)
	if err != nil {
		if err == apperrors.ErrArchivePasswordNotFound {
			return "", nil // No cached password found, not an error
		}
		s.logger.ErrorContext(ctx, "Failed to get cached password", "storageID", storageID, "archivePath", archivePath, "error", err)
		return "", err
	}

	// Update in-memory cache
	s.passwordCacheMutex.Lock()
	s.passwordCache[cacheKey] = cache.Password
	s.passwordCacheMutex.Unlock()

	// Update last used time
	go func() {
		timeoutCtx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
		defer cancel()
		_ = s.repo.UpdateCachedPasswordUsage(timeoutCtx, storageID, archivePath)
	}()

	return cache.Password, nil
}

// SetCachedPassword caches a successful password for a specific archive
func (s *ArchivePasswordService) SetCachedPassword(ctx context.Context, storageID uint, archivePath, password string) error {
	// Update in-memory cache immediately
	cacheKey := getCacheKey(storageID, archivePath)

	s.passwordCacheMutex.Lock()
	s.passwordCache[cacheKey] = password
	s.passwordCacheMutex.Unlock()

	cache := &models.ArchivePasswordCache{
		StorageID:   storageID,
		ArchivePath: archivePath,
		Password:    password,
		LastUsedAt:  time.Now(),
	}

	// Update database asynchronously
	go func() {
		timeoutCtx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
		defer cancel()

		if err := s.repo.SetCachedPassword(timeoutCtx, cache); err != nil {
			s.logger.ErrorContext(ctx, "Failed to cache password in database", "storageID", storageID, "archivePath", archivePath, "error", err)
		}
	}()

	return nil
}

// GetAllCachedPasswords retrieves all cached passwords for admin management
func (s *ArchivePasswordService) GetAllCachedPasswords(ctx context.Context) ([]*models.ArchivePasswordCacheResponse, error) {
	caches, err := s.repo.GetAllCachedPasswords(ctx)
	if err != nil {
		s.logger.ErrorContext(ctx, "Failed to get all cached passwords", "error", err)
		return nil, err
	}

	responses := make([]*models.ArchivePasswordCacheResponse, len(caches))
	for i, c := range caches {
		responses[i] = &models.ArchivePasswordCacheResponse{
			ID:          c.ID,
			StorageID:   c.StorageID,
			ArchivePath: c.ArchivePath,
			Password:    c.Password,
			LastUsedAt:  c.LastUsedAt,
			CreatedAt:   c.CreatedAt,
			UpdatedAt:   c.UpdatedAt,
		}
	}

	return responses, nil
}

// DeleteCachedPassword removes a cached password
func (s *ArchivePasswordService) DeleteCachedPassword(ctx context.Context, storageID uint, archivePath string) error {
	// Remove from in-memory cache
	cacheKey := getCacheKey(storageID, archivePath)

	s.passwordCacheMutex.Lock()
	delete(s.passwordCache, cacheKey)
	s.passwordCacheMutex.Unlock()

	if err := s.repo.DeleteCachedPassword(ctx, storageID, archivePath); err != nil {
		s.logger.ErrorContext(ctx, "Failed to delete cached password", "storageID", storageID, "archivePath", archivePath, "error", err)
		return err
	}
	return nil
}

// CleanupOldCachedPasswords removes cached passwords older than the specified duration
func (s *ArchivePasswordService) CleanupOldCachedPasswords(ctx context.Context, olderThan time.Duration) error {
	if err := s.repo.CleanupOldCachedPasswords(ctx, olderThan); err != nil {
		s.logger.ErrorContext(ctx, "Failed to cleanup old cached passwords", "olderThan", olderThan, "error", err)
		return err
	}

	// We should also refresh our in-memory cache after cleanup
	s.preloadPasswordCache(ctx)

	return nil
}
