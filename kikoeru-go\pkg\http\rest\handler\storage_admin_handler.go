package handler

import (
	"errors"
	"fmt"
	"log/slog"
	"net/http"
	"strconv"

	"github.com/Sakura-Byte/kikoeru-go/pkg/apperrors"
	dto_storage "github.com/Sakura-Byte/kikoeru-go/pkg/dto/storage" // Added DTO import
	"github.com/Sakura-Byte/kikoeru-go/pkg/http/rest/common"
	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
	"github.com/Sakura-Byte/kikoeru-go/pkg/ports" // Import ports package for service interface
	"github.com/gin-gonic/gin"
)

type StorageAdminHandler struct {
	service ports.StorageAdminService // Changed to ports.StorageAdminService
	logger  *slog.Logger
}

func NewStorageAdminHandler(service ports.StorageAdminService) *StorageAdminHandler { // Changed to ports.StorageAdminService
	baseLogger := slog.Default()
	return &StorageAdminHandler{
		service: service,
		logger:  baseLogger.With("handler", "StorageAdminHandler"),
	}
}

// --- Storage Driver Definition Handler ---

// GetDriverDefinitionsHandler handles GET /api/v1/admin/storage/driver-definitions
func (h *StorageAdminHandler) GetDriverDefinitionsHandler(c *gin.Context) {
	definitions, err := h.service.GetDriverDefinitions(c.Request.Context())
	if err != nil {
		h.logger.ErrorContext(c.Request.Context(), "Failed to get driver definitions", "error", err)
		common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to retrieve driver definitions")
		return
	}
	common.SendSuccessResponse(c, http.StatusOK, definitions)
}

// --- Storage Source Handlers ---

func (h *StorageAdminHandler) ListStorageSources(c *gin.Context) {
	sources, err := h.service.ListStorageSources(c.Request.Context())
	if err != nil {
		// Assuming service.ListStorageSources might return apperrors.ErrStorageSourceOperationFailed
		common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to list storage sources")
		return
	}
	common.SendSuccessResponse(c, http.StatusOK, sources)
}

func (h *StorageAdminHandler) GetStorageSource(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid storage source ID format")
		return
	}
	source, err := h.service.GetStorageSourceByID(c.Request.Context(), uint(id))
	if err != nil {
		if errors.Is(err, apperrors.ErrStorageNotFound) {
			common.SendErrorResponse(c, http.StatusNotFound, "Storage source not found")
		} else {
			common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to retrieve storage source")
		}
		return
	}
	common.SendSuccessResponse(c, http.StatusOK, source)
}

func (h *StorageAdminHandler) CreateStorageSource(c *gin.Context) {
	var req dto_storage.CreateStorageSourceRequest // Changed to dto_storage
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WarnContext(c.Request.Context(), "Failed to bind JSON for CreateStorageSource", "error", err)
		common.SendErrorResponse(c, http.StatusBadRequest, fmt.Sprintf("Invalid request payload: %v", err))
		return
	}

	createdStorage, err := h.service.CreateStorageSource(c.Request.Context(), req)
	if err != nil {
		if errors.Is(err, apperrors.ErrStorageSourceInvalidInput) {
			common.SendErrorResponse(c, http.StatusBadRequest, err.Error())
		} else {
			common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to create storage source")
		}
		return
	}
	common.SendSuccessResponse(c, http.StatusCreated, createdStorage)
}

// UpdateStorageSource handles PUT /api/v1/admin/storage/sources/:id - Restored
func (h *StorageAdminHandler) UpdateStorageSource(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid storage source ID format")
		return
	}

	var req dto_storage.UpdateStorageSourceRequest // Changed to dto_storage
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WarnContext(c.Request.Context(), "Failed to bind JSON for UpdateStorageSource", "error", err)
		common.SendErrorResponse(c, http.StatusBadRequest, fmt.Sprintf("Invalid request payload: %v", err))
		return
	}

	updatedStorage, err := h.service.UpdateStorageSource(c.Request.Context(), uint(id), req)
	if err != nil {
		if errors.Is(err, apperrors.ErrStorageNotFound) {
			common.SendErrorResponse(c, http.StatusNotFound, "Storage source not found")
		} else if errors.Is(err, apperrors.ErrStorageSourceInvalidInput) {
			common.SendErrorResponse(c, http.StatusBadRequest, err.Error())
		} else {
			common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to update storage source")
		}
		return
	}
	common.SendSuccessResponse(c, http.StatusOK, updatedStorage)
}

func (h *StorageAdminHandler) DeleteStorageSource(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid storage source ID format")
		return
	}
	err = h.service.DeleteStorageSource(c.Request.Context(), uint(id))
	if err != nil {
		if errors.Is(err, apperrors.ErrStorageNotFound) {
			common.SendErrorResponse(c, http.StatusNotFound, "Storage source not found")
		} else {
			common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to delete storage source")
		}
		return
	}
	common.SendSuccessResponse(c, http.StatusOK, gin.H{"message": fmt.Sprintf("Storage source %d deleted successfully", id)})
}

func (h *StorageAdminHandler) ReloadAllDriversHandler(c *gin.Context) {
	err := h.service.ReloadAllDrivers(c.Request.Context())
	if err != nil {
		// Assuming service.ReloadAllDrivers might return apperrors.ErrStorageSourceOperationFailed or similar
		common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to reload storage drivers")
		return
	}
	common.SendSuccessResponse(c, http.StatusOK, gin.H{"message": "Storage drivers reload initiated successfully"})
}

func (h *StorageAdminHandler) TestStorageConnection(c *gin.Context) {
	var storageConfig models.StorageSource
	if err := c.ShouldBindJSON(&storageConfig); err != nil {
		h.logger.WarnContext(c.Request.Context(), "Failed to bind JSON for TestStorageConnection", "error", err)
		common.SendErrorResponse(c, http.StatusBadRequest, fmt.Sprintf("Invalid request payload: %v", err))
		return
	}
	err := h.service.TestStorageSourceConnection(c.Request.Context(), storageConfig)
	if err != nil {
		// Assuming service.TestStorageSourceConnection might return apperrors.ErrStorageSourceInvalidInput or a wrapped driver error
		errMsg := fmt.Sprintf("Connection test failed: %s", err.Error())
		h.logger.InfoContext(c.Request.Context(), "Storage connection test failed", "driver", storageConfig.Driver, "error", err)
		common.SendErrorResponse(c, http.StatusBadRequest, errMsg)
		return
	}
	common.SendSuccessResponse(c, http.StatusOK, gin.H{"message": "Storage connection test successful"})
}
