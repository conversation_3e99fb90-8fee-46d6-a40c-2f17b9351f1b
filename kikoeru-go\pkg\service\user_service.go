package service

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"log/slog"
	"time"

	"github.com/Sakura-Byte/kikoeru-go/pkg/apperrors"
	"github.com/Sakura-Byte/kikoeru-go/pkg/auth"
	"github.com/Sakura-Byte/kikoeru-go/pkg/config"
	user_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/user"
	"github.com/Sakura-Byte/kikoeru-go/pkg/email"
	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
	"github.com/Sakura-Byte/kikoeru-go/pkg/ports"
	"github.com/Sakura-Byte/kikoeru-go/pkg/storage/database"
)

const DefaultUserGroup = "user"

// UserService interface has been moved to github.com/Sakura-Byte/kikoeru-go/pkg/ports

type userService struct {
	userRepo    database.UserRepository
	cfg         *config.AppConfig
	logger      *slog.Logger
	emailSender email.EmailSender
}

// Ensure userService implements ports.UserService
var _ ports.UserService = (*userService)(nil)

func NewUserService(
	userRepo database.UserRepository,
	cfg *config.AppConfig,
	logger *slog.Logger,
	emailSender email.EmailSender,
) ports.UserService {
	return &userService{
		userRepo:    userRepo,
		cfg:         cfg,
		logger:      logger.With("service", "UserService"),
		emailSender: emailSender,
	}
}

func (s *userService) RegisterUser(ctx context.Context, req user_dto.UserRegistrationRequest, ipAddress string) (*user_dto.UserResponse, error) {
	if !s.cfg.Auth.AllowRegistration {
		return nil, apperrors.ErrRegistrationDisabled
	}
	if req.Username == "" {
		return nil, fmt.Errorf("%w: username cannot be empty", apperrors.ErrValidation)
	}
	if len(req.Password) < s.cfg.Auth.MinPasswordLength {
		return nil, fmt.Errorf("%w: password too short (min %d chars)", apperrors.ErrValidation, s.cfg.Auth.MinPasswordLength)
	}

	group := DefaultUserGroup

	if _, err := s.userRepo.GetByUsername(ctx, req.Username); !errors.Is(err, apperrors.ErrUserNotFound) {
		if err == nil {
			return nil, apperrors.ErrUserAlreadyExists
		}
		return nil, fmt.Errorf("failed to check username existence: %w", err)
	}

	user := &models.User{
		Username: req.Username,
		Password: "",
		Group:    group,
	}

	if s.cfg.Auth.EnableEmailFeatures {
		if s.cfg.Auth.EnsureEmail {
			if req.Email == "" {
				return nil, apperrors.ErrEmailRequired
			}
			if existingUser, err := s.userRepo.GetByEmail(ctx, req.Email); !errors.Is(err, apperrors.ErrUserNotFound) {
				if err == nil && existingUser != nil && existingUser.EmailVerified {
					return nil, apperrors.ErrRegistrationEmailInUse
				}
				if err != nil {
					return nil, fmt.Errorf("failed to check email existence: %w", err)
				}
			}
			user.Email = sql.NullString{String: req.Email, Valid: true}
			user.EmailVerified = false
			token, tokenErr := auth.GenerateSecureRandomString(auth.DefaultTokenLength)
			if tokenErr != nil {
				return nil, fmt.Errorf("failed to generate verification token: %w", tokenErr)
			}
			user.VerificationToken = sql.NullString{String: token, Valid: true}
			user.VerificationTokenExpiresAt = sql.NullTime{Time: time.Now().Add(s.cfg.Auth.VerificationTokenTTL), Valid: true}
			verificationLink := fmt.Sprintf("%s/verify-email?token=%s", s.cfg.Server.PublicURL, token)
			emailBody := fmt.Sprintf("Please verify your email address by clicking the link: %s", verificationLink)
			if sendErr := s.emailSender.SendEmail(user.Email.String, "Verify Your Email Address", emailBody, ipAddress); sendErr != nil {
				s.logger.ErrorContext(ctx, "Failed to send verification email during registration", "recipient", user.Email.String, "ip", ipAddress, "error", sendErr)
			}
		} else {
			user.Email = sql.NullString{Valid: false}
			user.EmailVerified = false
		}
	} else {
		user.Email = sql.NullString{Valid: false}
		user.EmailVerified = false
	}

	hashedPassword, err := auth.HashPassword(req.Password, s.cfg.Auth.BcryptCost)
	if err != nil {
		return nil, fmt.Errorf("failed to hash password: %w", err)
	}
	user.Password = hashedPassword

	err = s.userRepo.Create(ctx, user)
	if err != nil {
		return nil, fmt.Errorf("failed to create user: %w", err)
	}
	userResponse := user.ToUserResponse()
	return &userResponse, nil
}

func (s *userService) LoginUser(ctx context.Context, req user_dto.UserLoginRequest) (string, *user_dto.UserResponse, error) {
	if req.Identifier == "" || req.Password == "" {
		return "", nil, fmt.Errorf("%w: identifier and password cannot be empty", apperrors.ErrValidation)
	}
	user, err := s.userRepo.GetByIdentifier(ctx, req.Identifier)
	if err != nil {
		return "", nil, apperrors.ErrInvalidCredentials
	}

	if !auth.CheckPasswordHash(req.Password, user.Password) {
		return "", nil, apperrors.ErrInvalidCredentials
	}

	if s.cfg.Auth.EnableEmailFeatures && s.cfg.Auth.EnsureEmail && user.Email.Valid && !user.EmailVerified {
		return "", nil, apperrors.ErrEmailNotVerified
	}

	jwtExpiresIn := s.cfg.Auth.JWTExpiresIn
	if jwtExpiresIn <= 0 {
		jwtExpiresIn = 24 * time.Hour
	}
	tokenString, err := auth.GenerateToken(user.ID, user.Username, user.Group, s.cfg.GetJWTSecretBytes(), jwtExpiresIn)
	if err != nil {
		return "", nil, fmt.Errorf("failed to generate token: %w", err)
	}
	userResponse := user.ToUserResponse()
	return tokenString, &userResponse, nil
}

func (s *userService) ChangeUserPassword(ctx context.Context, username string, req user_dto.ChangePasswordRequest) error {
	if username == "" {
		return fmt.Errorf("%w: username cannot be empty", apperrors.ErrValidation)
	}
	if len(req.NewPassword) < s.cfg.Auth.MinPasswordLength {
		return fmt.Errorf("%w: new password too short (min %d chars)", apperrors.ErrValidation, s.cfg.Auth.MinPasswordLength)
	}

	user, err := s.userRepo.GetByUsername(ctx, username)
	if err != nil {
		if errors.Is(err, apperrors.ErrUserNotFound) {
			return apperrors.ErrUserNotFound
		}
		return err
	}
	if user.Group == models.UserGroupGuest {
		return apperrors.ErrGuestActionNotAllowed
	}
	if !auth.CheckPasswordHash(req.OldPassword, user.Password) {
		return apperrors.ErrInvalidCredentials
	}

	hashedNewPassword, err := auth.HashPassword(req.NewPassword, s.cfg.Auth.BcryptCost)
	if err != nil {
		return fmt.Errorf("failed to hash new password: %w", err)
	}

	user.Password = hashedNewPassword
	return s.userRepo.Update(ctx, user)
}

func (s *userService) GetUserByName(ctx context.Context, username string) (*user_dto.UserResponse, error) {
	if username == "" {
		return nil, fmt.Errorf("%w: username cannot be empty", apperrors.ErrValidation)
	}
	user, err := s.userRepo.GetByUsername(ctx, username)
	if err != nil {
		if errors.Is(err, apperrors.ErrUserNotFound) {
			return nil, apperrors.ErrUserNotFound
		}
		return nil, err
	}
	userResponse := user.ToUserResponse()
	return &userResponse, nil
}

func (s *userService) GetUserByID(ctx context.Context, userID string) (*user_dto.UserResponse, error) {
	if userID == "" {
		return nil, fmt.Errorf("%w: userID cannot be empty", apperrors.ErrValidation)
	}
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		if errors.Is(err, apperrors.ErrUserNotFound) {
			return nil, apperrors.ErrUserNotFound
		}
		return nil, err
	}
	userResponse := user.ToUserResponse()
	return &userResponse, nil
}

func (s *userService) RequestLinkEmail(ctx context.Context, username string, newEmail string, ipAddress string) error {
	if !s.cfg.Auth.EnableEmailFeatures {
		return errors.New("email features are disabled")
	}
	if newEmail == "" {
		return fmt.Errorf("%w: email cannot be empty for linking", apperrors.ErrValidation)
	}

	user, err := s.userRepo.GetByUsername(ctx, username)
	if err != nil {
		if errors.Is(err, apperrors.ErrUserNotFound) {
			return apperrors.ErrUserNotFound
		}
		return err
	}
	if user.Group == models.UserGroupGuest {
		return apperrors.ErrGuestActionNotAllowed
	}

	if existingUser, _ := s.userRepo.GetByEmail(ctx, newEmail); existingUser != nil && existingUser.ID != user.ID && existingUser.EmailVerified {
		return apperrors.ErrEmailInUseByAnotherAccount
	}

	if user.Email.Valid && user.Email.String == newEmail && user.EmailVerified {
		return apperrors.ErrEmailAlreadyLinkedAndVerified
	}

	token, tokenErr := auth.GenerateSecureRandomString(auth.DefaultTokenLength)
	if tokenErr != nil {
		return fmt.Errorf("failed to generate verification token: %w", tokenErr)
	}

	user.Email = sql.NullString{String: newEmail, Valid: true}
	user.EmailVerified = false
	user.VerificationToken = sql.NullString{String: token, Valid: true}
	user.VerificationTokenExpiresAt = sql.NullTime{Time: time.Now().Add(s.cfg.Auth.VerificationTokenTTL), Valid: true}
	if errUpdate := s.userRepo.Update(ctx, user); errUpdate != nil {
		return fmt.Errorf("failed to update user for email link/change: %w", errUpdate)
	}

	verificationLink := fmt.Sprintf("%s/verify-email?token=%s", s.cfg.Server.PublicURL, token)
	emailBody := fmt.Sprintf("Please verify your email address %s by clicking the link: %s", newEmail, verificationLink)
	if sendErr := s.emailSender.SendEmail(user.Email.String, "Verify Your Email Address", emailBody, ipAddress); sendErr != nil {
		s.logger.ErrorContext(ctx, "Failed to send verification email for linking/changing email", "recipient", user.Email.String, "ip", ipAddress, "error", sendErr)
	}
	return nil
}

func (s *userService) VerifyUserEmail(ctx context.Context, verificationToken string) error {
	if !s.cfg.Auth.EnableEmailFeatures {
		return errors.New("email features are disabled")
	}
	if verificationToken == "" {
		return fmt.Errorf("%w: verification token cannot be empty", apperrors.ErrValidation)
	}

	user, err := s.userRepo.GetByVerificationToken(ctx, verificationToken)
	if err != nil {
		if errors.Is(err, apperrors.ErrUserNotFound) {
			return apperrors.ErrInvalidOrExpiredToken
		}
		return err
	}

	if !user.VerificationTokenExpiresAt.Valid || time.Now().After(user.VerificationTokenExpiresAt.Time) {
		user.VerificationToken = sql.NullString{Valid: false}
		user.VerificationTokenExpiresAt = sql.NullTime{Valid: false}
		_ = s.userRepo.Update(ctx, user)
		return apperrors.ErrInvalidOrExpiredToken
	}

	user.EmailVerified = true
	user.VerificationToken = sql.NullString{Valid: false}
	user.VerificationTokenExpiresAt = sql.NullTime{Valid: false}
	return s.userRepo.Update(ctx, user)
}

func (s *userService) UnlinkUserEmail(ctx context.Context, username string, password string) error {
	if !s.cfg.Auth.EnableEmailFeatures {
		s.logger.InfoContext(ctx, "Attempted to unlink email, but email features are disabled.", "username", username)
		return nil
	}
	user, err := s.userRepo.GetByUsername(ctx, username)
	if err != nil {
		if errors.Is(err, apperrors.ErrUserNotFound) {
			return apperrors.ErrUserNotFound
		}
		return err
	}
	if user.Group == models.UserGroupGuest {
		return apperrors.ErrGuestActionNotAllowed
	}

	if !auth.CheckPasswordHash(password, user.Password) {
		return apperrors.ErrInvalidCredentials
	}

	if !user.Email.Valid {
		s.logger.InfoContext(ctx, "User attempted to unlink email, but no email was linked.", "username", username)
		return nil
	}

	user.Email = sql.NullString{Valid: false}
	user.EmailVerified = false
	user.VerificationToken = sql.NullString{Valid: false}
	user.VerificationTokenExpiresAt = sql.NullTime{Valid: false}
	return s.userRepo.Update(ctx, user)
}

func (s *userService) RequestPasswordReset(ctx context.Context, email string, ipAddress string) error {
	if !s.cfg.Auth.EnableEmailFeatures {
		s.logger.InfoContext(ctx, "Password reset requested but email features are disabled.", "email", email, "ip", ipAddress)
		return nil
	}
	if email == "" {
		return fmt.Errorf("%w: email cannot be empty for password reset", apperrors.ErrValidation)
	}

	user, err := s.userRepo.GetByEmail(ctx, email)
	if err != nil || user == nil || !user.Email.Valid || user.Email.String == "" || !user.EmailVerified {
		s.logger.InfoContext(ctx, "Password reset requested for non-existent, non-linked, or unverified email. No action taken.", "email", email, "ip", ipAddress)
		return nil
	}
	if user.Group == models.UserGroupGuest {
		s.logger.InfoContext(ctx, "Password reset requested for a guest account. No action taken.", "email", email, "ip", ipAddress)
		return nil
	}

	token, tokenErr := auth.GenerateSecureRandomString(auth.DefaultTokenLength)
	if tokenErr != nil {
		return fmt.Errorf("failed to generate password reset token: %w", tokenErr)
	}

	user.ResetPasswordToken = sql.NullString{String: token, Valid: true}
	user.ResetPasswordTokenExpiresAt = sql.NullTime{Time: time.Now().Add(s.cfg.Auth.PasswordResetTokenTTL), Valid: true}
	if errUpdate := s.userRepo.Update(ctx, user); errUpdate != nil {
		return fmt.Errorf("failed to update user for password reset: %w", errUpdate)
	}

	resetLink := fmt.Sprintf("%s/reset-password?token=%s", s.cfg.Server.PublicURL, token)
	emailBody := fmt.Sprintf("You requested a password reset. Click the link to reset your password: %s", resetLink)
	if sendErr := s.emailSender.SendEmail(user.Email.String, "Password Reset Request", emailBody, ipAddress); sendErr != nil {
		s.logger.ErrorContext(ctx, "Failed to send password reset email", "recipient", user.Email.String, "ip", ipAddress, "error", sendErr)
	}
	return nil
}

func (s *userService) ResetPasswordWithToken(ctx context.Context, req user_dto.ResetPasswordWithTokenRequest) error {
	if !s.cfg.Auth.EnableEmailFeatures {
		return errors.New("email features are disabled for password reset")
	}
	if req.Token == "" {
		return fmt.Errorf("%w: reset token cannot be empty", apperrors.ErrValidation)
	}
	if len(req.NewPassword) < s.cfg.Auth.MinPasswordLength {
		return fmt.Errorf("%w: new password too short (min %d chars)", apperrors.ErrValidation, s.cfg.Auth.MinPasswordLength)
	}

	user, err := s.userRepo.GetByResetPasswordToken(ctx, req.Token)
	if err != nil {
		if errors.Is(err, apperrors.ErrUserNotFound) {
			return apperrors.ErrInvalidOrExpiredToken
		}
		return err
	}

	if !user.ResetPasswordTokenExpiresAt.Valid || time.Now().After(user.ResetPasswordTokenExpiresAt.Time) {
		user.ResetPasswordToken = sql.NullString{Valid: false}
		user.ResetPasswordTokenExpiresAt = sql.NullTime{Valid: false}
		_ = s.userRepo.Update(ctx, user)
		return apperrors.ErrInvalidOrExpiredToken
	}

	hashedNewPassword, hashErr := auth.HashPassword(req.NewPassword, s.cfg.Auth.BcryptCost)
	if hashErr != nil {
		return fmt.Errorf("failed to hash new password: %w", hashErr)
	}

	user.Password = hashedNewPassword
	user.ResetPasswordToken = sql.NullString{Valid: false}
	user.ResetPasswordTokenExpiresAt = sql.NullTime{Valid: false}
	return s.userRepo.Update(ctx, user)
}

func (s *userService) ListUsers(ctx context.Context, params user_dto.ListUsersParams) ([]user_dto.UserResponse, int64, error) {
	s.logger.DebugContext(ctx, "Admin listing users", "params", fmt.Sprintf("%+v", params))
	dbParams := database.ListUsersAdminDbParams{
		PaginationParams: params.PaginationParams,
		Username:         params.Username,
		Email:            params.Email,
		Group:            params.Group,
	}
	users, totalCount, err := s.userRepo.List(ctx, dbParams)
	if err != nil {
		s.logger.ErrorContext(ctx, "Failed to list users from repository for admin", "params", dbParams, "error", err)
		return nil, 0, fmt.Errorf("failed to retrieve users: %w", err)
	}
	userResponses := make([]user_dto.UserResponse, len(users))
	for i, u := range users {
		userResponses[i] = u.ToUserResponse()
	}
	s.logger.InfoContext(ctx, "Admin listed users", "count", len(userResponses), "total_available", totalCount)
	return userResponses, totalCount, nil
}

func (s *userService) GetUserForAdmin(ctx context.Context, userID string) (*user_dto.UserResponse, error) {
	s.logger.DebugContext(ctx, "Admin getting user by ID", "target_user_id", userID)
	if userID == "" {
		return nil, fmt.Errorf("%w: userID cannot be empty", apperrors.ErrValidation)
	}
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		if errors.Is(err, apperrors.ErrUserNotFound) {
			return nil, apperrors.ErrUserNotFound
		}
		s.logger.ErrorContext(ctx, "Failed to get user by ID from repository for admin", "target_user_id", userID, "error", err)
		return nil, fmt.Errorf("failed to retrieve user: %w", err)
	}
	responseDto := user.ToUserResponse()
	return &responseDto, nil
}

func (s *userService) UpdateUserByAdmin(ctx context.Context, userID string, req user_dto.UpdateUserByAdminRequest) (*user_dto.UserResponse, error) {
	s.logger.DebugContext(ctx, "Admin updating user", "target_user_id", userID, "request", fmt.Sprintf("%+v", req))
	if userID == "" {
		return nil, fmt.Errorf("%w: userID cannot be empty for update", apperrors.ErrValidation)
	}
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		if errors.Is(err, apperrors.ErrUserNotFound) {
			return nil, apperrors.ErrUserNotFound
		}
		s.logger.ErrorContext(ctx, "Failed to get user by ID from repository for admin update", "target_user_id", userID, "error", err)
		return nil, fmt.Errorf("failed to retrieve user for update: %w", err)
	}

	updated := false
	if req.Username != nil && *req.Username != "" && user.Username != *req.Username {
		if existing, _ := s.userRepo.GetByUsername(ctx, *req.Username); existing != nil && existing.ID != user.ID {
			return nil, fmt.Errorf("%w: username '%s' is already taken", apperrors.ErrUserAlreadyExists, *req.Username)
		}
		user.Username = *req.Username
		updated = true
	}
	if req.Email != nil {
		if *req.Email == "" {
			if user.Email.Valid {
				user.Email = sql.NullString{Valid: false}
				user.EmailVerified = false
				updated = true
			}
		} else {
			if existing, _ := s.userRepo.GetByEmail(ctx, *req.Email); existing != nil && existing.ID != user.ID && existing.EmailVerified {
				return nil, fmt.Errorf("%w: email '%s' is already in use by another verified account", apperrors.ErrEmailInUseByAnotherAccount, *req.Email)
			}
			if !user.Email.Valid || user.Email.String != *req.Email {
				user.Email = sql.NullString{String: *req.Email, Valid: true}
				if req.EmailVerified != nil {
					user.EmailVerified = *req.EmailVerified
				} else if user.Email.String != *req.Email {
					user.EmailVerified = false
				}
				updated = true
			}
		}
	}
	if req.Password != nil && *req.Password != "" {
		if len(*req.Password) < s.cfg.Auth.MinPasswordLength {
			return nil, fmt.Errorf("%w: new password too short (min %d chars)", apperrors.ErrValidation, s.cfg.Auth.MinPasswordLength)
		}
		hashedNewPassword, err := auth.HashPassword(*req.Password, s.cfg.Auth.BcryptCost)
		if err != nil {
			return nil, fmt.Errorf("failed to hash new password: %w", err)
		}
		user.Password = hashedNewPassword
		updated = true
	}
	if req.EmailVerified != nil && user.Email.Valid && user.EmailVerified != *req.EmailVerified {
		user.EmailVerified = *req.EmailVerified
		updated = true
	}
	if req.Group != nil && user.Group != *req.Group {
		if *req.Group != models.UserGroupAdmin && *req.Group != models.UserGroupUser && *req.Group != models.UserGroupGuest {
			return nil, fmt.Errorf("%w: invalid user group '%s'", apperrors.ErrValidation, *req.Group)
		}
		user.Group = *req.Group
		updated = true
	}

	if !updated {
		s.logger.InfoContext(ctx, "No changes detected for user update by admin", "target_user_id", userID)
		responseDto := user.ToUserResponse()
		return &responseDto, nil
	}

	if err := s.userRepo.Update(ctx, user); err != nil {
		s.logger.ErrorContext(ctx, "Failed to update user in repository by admin", "target_user_id", userID, "error", err)
		return nil, fmt.Errorf("failed to update user: %w", err)
	}
	s.logger.InfoContext(ctx, "User updated successfully by admin", "target_user_id", userID)
	updatedUser, err := s.userRepo.GetByID(ctx, user.ID)
	if err != nil {
		s.logger.ErrorContext(ctx, "Failed to re-fetch user after admin update", "user_id", user.ID, "error", err)
		responseDtoFallback := user.ToUserResponse()
		return &responseDtoFallback, nil
	}
	responseDto := updatedUser.ToUserResponse()
	return &responseDto, nil
}

func (s *userService) DeleteUserByAdmin(ctx context.Context, userID string) error {
	s.logger.DebugContext(ctx, "Admin deleting user", "target_user_id", userID)
	if userID == "" {
		return fmt.Errorf("%w: userID cannot be empty for deletion", apperrors.ErrValidation)
	}
	_, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		if errors.Is(err, apperrors.ErrUserNotFound) {
			return apperrors.ErrUserNotFound
		}
		s.logger.ErrorContext(ctx, "Failed to get user for admin deletion check", "target_user_id", userID, "error", err)
		return fmt.Errorf("failed to retrieve user for deletion: %w", err)
	}
	err = s.userRepo.DeleteByID(ctx, userID)
	if err != nil {
		if errors.Is(err, apperrors.ErrUserNotFound) {
			return apperrors.ErrUserNotFound
		}
		s.logger.ErrorContext(ctx, "Failed to delete user from repository by admin", "target_user_id", userID, "error", err)
		return fmt.Errorf("failed to delete user: %w", err)
	}
	s.logger.InfoContext(ctx, "User deleted successfully by admin", "target_user_id", userID)
	return nil
}

// UpdateAdminSelf implements the method for an admin to update their own username and password without requiring the old password.
func (s *userService) UpdateAdminSelf(ctx context.Context, adminUsername string, req user_dto.AdminUpdateSelfRequest) (*user_dto.UserResponse, error) {
	s.logger.DebugContext(ctx, "Admin updating self", "admin_username", adminUsername)
	if adminUsername == "" {
		return nil, fmt.Errorf("%w: admin username cannot be empty", apperrors.ErrValidation)
	}

	user, err := s.userRepo.GetByUsername(ctx, adminUsername)
	if err != nil {
		if errors.Is(err, apperrors.ErrUserNotFound) {
			return nil, apperrors.ErrUserNotFound
		}
		s.logger.ErrorContext(ctx, "Failed to get admin user for self-update", "admin_username", adminUsername, "error", err)
		return nil, fmt.Errorf("failed to retrieve admin user: %w", err)
	}

	// Verify that the user is an admin
	if user.Group != models.UserGroupAdmin {
		s.logger.WarnContext(ctx, "Non-admin user attempted to use admin self-update endpoint", "username", adminUsername, "group", user.Group)
		return nil, apperrors.ErrAdminAccessDenied
	}

	updated := false

	// Update username if provided
	if req.Username != nil && *req.Username != "" && user.Username != *req.Username {
		// Check if the new username is already taken by another user
		if existing, _ := s.userRepo.GetByUsername(ctx, *req.Username); existing != nil && existing.ID != user.ID {
			return nil, fmt.Errorf("%w: username '%s' is already taken", apperrors.ErrUserAlreadyExists, *req.Username)
		}
		user.Username = *req.Username
		updated = true
	}

	// Update password if provided
	if req.NewPassword != nil && *req.NewPassword != "" {
		if len(*req.NewPassword) < s.cfg.Auth.MinPasswordLength {
			return nil, fmt.Errorf("%w: new password too short (min %d chars)", apperrors.ErrValidation, s.cfg.Auth.MinPasswordLength)
		}

		hashedNewPassword, err := auth.HashPassword(*req.NewPassword, s.cfg.Auth.BcryptCost)
		if err != nil {
			return nil, fmt.Errorf("failed to hash new password: %w", err)
		}

		user.Password = hashedNewPassword
		updated = true
	}

	if !updated {
		s.logger.InfoContext(ctx, "No changes detected for admin self-update", "admin_username", adminUsername)
		responseDto := user.ToUserResponse()
		return &responseDto, nil
	}

	if err := s.userRepo.Update(ctx, user); err != nil {
		s.logger.ErrorContext(ctx, "Failed to update admin user", "admin_username", adminUsername, "error", err)
		return nil, fmt.Errorf("failed to update admin user: %w", err)
	}

	s.logger.InfoContext(ctx, "Admin user updated successfully", "admin_username", adminUsername)
	updatedUser, err := s.userRepo.GetByID(ctx, user.ID)
	if err != nil {
		s.logger.ErrorContext(ctx, "Failed to re-fetch admin user after update", "user_id", user.ID, "error", err)
		responseDtoFallback := user.ToUserResponse()
		return &responseDtoFallback, nil
	}
	responseDto := updatedUser.ToUserResponse()
	return &responseDto, nil
}

// CreateUserByAdmin implements the method for an admin to create a new user.
func (s *userService) CreateUserByAdmin(ctx context.Context, req user_dto.AdminCreateUserRequest) (*user_dto.UserResponse, error) {
	s.logger.DebugContext(ctx, "Admin creating new user", "new_username", req.Username)
	if req.Username == "" {
		return nil, fmt.Errorf("%w: username cannot be empty", apperrors.ErrValidation)
	}
	if len(req.Password) < s.cfg.Auth.MinPasswordLength {
		return nil, fmt.Errorf("%w: password too short (min %d chars)", apperrors.ErrValidation, s.cfg.Auth.MinPasswordLength)
	}
	if req.Group != models.UserGroupAdmin && req.Group != models.UserGroupUser && req.Group != models.UserGroupGuest {
		return nil, fmt.Errorf("%w: invalid user group '%s'", apperrors.ErrValidation, req.Group)
	}

	// Check if username is already taken
	if _, err := s.userRepo.GetByUsername(ctx, req.Username); !errors.Is(err, apperrors.ErrUserNotFound) {
		if err == nil {
			return nil, fmt.Errorf("%w: username '%s' is already taken", apperrors.ErrUserAlreadyExists, req.Username)
		}
		return nil, fmt.Errorf("failed to check username existence: %w", err)
	}

	// Hash the password
	hashedPassword, err := auth.HashPassword(req.Password, s.cfg.Auth.BcryptCost)
	if err != nil {
		return nil, fmt.Errorf("failed to hash password: %w", err)
	}

	// Create user model
	user := &models.User{
		Username: req.Username,
		Password: hashedPassword,
		Group:    req.Group,
	}

	// Handle email if provided
	if req.Email != nil && *req.Email != "" {
		// Check if email is already in use
		if existingUser, err := s.userRepo.GetByEmail(ctx, *req.Email); !errors.Is(err, apperrors.ErrUserNotFound) {
			if err == nil && existingUser != nil && existingUser.EmailVerified {
				return nil, fmt.Errorf("%w: email '%s' is already in use by a verified account", apperrors.ErrEmailInUseByAnotherAccount, *req.Email)
			}
			if err != nil {
				return nil, fmt.Errorf("failed to check email existence: %w", err)
			}
		}
		user.Email = sql.NullString{String: *req.Email, Valid: true}

		// Handle email verification status
		if req.EmailVerified != nil {
			user.EmailVerified = *req.EmailVerified
		} else {
			user.EmailVerified = false
		}
	} else {
		user.Email = sql.NullString{Valid: false}
		user.EmailVerified = false
	}

	// Create the user in the database
	err = s.userRepo.Create(ctx, user)
	if err != nil {
		return nil, fmt.Errorf("failed to create user: %w", err)
	}

	s.logger.InfoContext(ctx, "User created successfully by admin", "new_user_id", user.ID, "new_username", user.Username)
	userResponse := user.ToUserResponse()
	return &userResponse, nil
}

// AdminUpdateOwnProfile allows an admin to update their own profile without old password verification
func (s *userService) AdminUpdateOwnProfile(ctx context.Context, adminUsername string, newUsername, newPassword *string) (*user_dto.UserResponse, error) {
	s.logger.InfoContext(ctx, "Admin updating own profile", "admin_username", adminUsername)

	// Get admin user
	user, err := s.userRepo.GetByUsername(ctx, adminUsername)
	if err != nil {
		if errors.Is(err, apperrors.ErrUserNotFound) {
			return nil, apperrors.ErrUserNotFound
		}
		s.logger.ErrorContext(ctx, "Failed to get admin user", "error", err)
		return nil, fmt.Errorf("failed to get admin user: %w", err)
	}

	// Check if the user is actually an admin
	if user.Group != models.UserGroupAdmin {
		s.logger.ErrorContext(ctx, "Non-admin user attempting to use admin self-update", "username", adminUsername)
		return nil, apperrors.ErrForbidden
	}

	updated := false

	// Update username if provided
	if newUsername != nil && *newUsername != "" && *newUsername != adminUsername {
		// Check if the new username is already taken by another user
		existingUser, err := s.userRepo.GetByUsername(ctx, *newUsername)
		if err == nil && existingUser.ID != user.ID {
			return nil, fmt.Errorf("%w: username '%s' is already taken", apperrors.ErrUserAlreadyExists, *newUsername)
		} else if err != nil && !errors.Is(err, apperrors.ErrUserNotFound) {
			s.logger.ErrorContext(ctx, "Failed to check if username exists", "error", err)
			return nil, fmt.Errorf("failed to check if username exists: %w", err)
		}

		user.Username = *newUsername
		updated = true
	}

	// Update password if provided
	if newPassword != nil && *newPassword != "" {
		if len(*newPassword) < s.cfg.Auth.MinPasswordLength {
			return nil, fmt.Errorf("%w: new password too short (min %d chars)", apperrors.ErrValidation, s.cfg.Auth.MinPasswordLength)
		}

		hashedNewPassword, err := auth.HashPassword(*newPassword, s.cfg.Auth.BcryptCost)
		if err != nil {
			return nil, fmt.Errorf("failed to hash new password: %w", err)
		}

		user.Password = hashedNewPassword
		updated = true
	}

	if !updated {
		s.logger.InfoContext(ctx, "No changes detected for admin self-update", "admin_username", adminUsername)
		responseDto := user.ToUserResponse()
		return &responseDto, nil
	}

	// Save changes
	if err := s.userRepo.Update(ctx, user); err != nil {
		s.logger.ErrorContext(ctx, "Failed to update admin user", "admin_username", adminUsername, "error", err)
		return nil, fmt.Errorf("failed to update admin user: %w", err)
	}

	s.logger.InfoContext(ctx, "Admin user updated successfully", "admin_username", adminUsername)
	updatedUser, err := s.userRepo.GetByID(ctx, user.ID)
	if err != nil {
		s.logger.ErrorContext(ctx, "Failed to re-fetch admin user after update", "user_id", user.ID, "error", err)
		responseDtoFallback := user.ToUserResponse()
		return &responseDtoFallback, nil
	}

	responseDto := updatedUser.ToUserResponse()
	return &responseDto, nil
}

// AdminSelfUpdateProfile implements the AdminSelfUpdateProfile method in the UserService interface
// This method uses AdminSelfUpdateRequest DTO
func (s *userService) AdminSelfUpdateProfile(ctx context.Context, adminUsername string, req user_dto.AdminSelfUpdateRequest) (*user_dto.UserResponse, error) {
	return s.AdminUpdateOwnProfile(ctx, adminUsername, req.NewUsername, req.NewPassword)
}

// CountUsers returns the total number of users in the database
func (s *userService) CountUsers() (int64, error) {
	// Get a context with a short timeout
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Using the ListUsers implementation to get the count
	params := database.ListUsersAdminDbParams{}
	_, count, err := s.userRepo.List(ctx, params)
	if err != nil {
		s.logger.Error("Failed to count users", "error", err)
		return 0, fmt.Errorf("failed to count users: %w", err)
	}

	return count, nil
}
