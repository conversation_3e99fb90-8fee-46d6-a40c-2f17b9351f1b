package handler

import (
	"errors"
	"fmt"
	"log/slog"
	"net/http"

	// "strconv" // Removed unused import
	// "strings" // Removed unused import

	"github.com/Sakura-Byte/kikoeru-go/pkg/apperrors"
	task_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/task" // Added for task payloads
	"github.com/Sakura-Byte/kikoeru-go/pkg/http/middleware"
	"github.com/Sakura-Byte/kikoeru-go/pkg/http/rest/common"
	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
	"github.com/Sakura-Byte/kikoeru-go/pkg/ports"
	"github.com/Sakura-Byte/kikoeru-go/pkg/storage/database"
	"github.com/gin-gonic/gin"
)

type BackgroundTaskHandler struct {
	service ports.BackgroundTaskService
	logger  *slog.Logger
}

func NewBackgroundTaskHandler(service ports.BackgroundTaskService, logger *slog.Logger) *BackgroundTaskHandler {
	return &BackgroundTaskHandler{
		service: service,
		logger:  logger.With("handler", "BackgroundTaskHandler"),
	}
}

// mapBackgroundTaskToDTO converts a models.BackgroundTask to a task_dto.BackgroundTaskResponseDTO.
func mapBackgroundTaskToDTO(taskModel *models.BackgroundTask) *task_dto.BackgroundTaskResponseDTO {
	if taskModel == nil {
		return nil
	}
	return &task_dto.BackgroundTaskResponseDTO{
		ID:                taskModel.ID,
		TaskType:          string(taskModel.TaskType),
		Status:            string(taskModel.Status),
		Progress:          taskModel.Progress,
		Message:           taskModel.Message,
		Payload:           taskModel.Payload,
		Result:            taskModel.Result,
		Error:             taskModel.Error,
		SubmittedByUserID: taskModel.SubmittedByUserID,
		CreatedAt:         taskModel.CreatedAt,
		StartedAt:         taskModel.StartedAt,
		CompletedAt:       taskModel.CompletedAt,
		UpdatedAt:         taskModel.UpdatedAt,
	}
}

// --- DTOs for Task Submission are now in pkg/dto/task ---

// --- Handler Methods ---

// SubmitScanLibraryTask handles POST /api/v1/admin/tasks/scan-library
func (h *BackgroundTaskHandler) SubmitScanLibraryTask(c *gin.Context) {
	authClaims := middleware.GetUserClaims(c)

	var req task_dto.ScanLibraryTaskPayload
	if err := c.ShouldBindJSON(&req); err != nil {
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid request payload: "+err.Error())
		return
	}
	// payload variable is now directly req
	task, err := h.service.SubmitTask(c.Request.Context(), models.TaskTypeScanLibrary, req, authClaims)
	if err != nil {
		h.logger.ErrorContext(c.Request.Context(), "Failed to submit scan library task", "storage_id", req.StorageID, "error", err)
		common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to submit scan library task: "+err.Error())
		return
	}
	common.SendSuccessResponse(c, http.StatusAccepted, mapBackgroundTaskToDTO(task))
}

// SubmitScrapeAllWorksTask handles POST /api/v1/admin/tasks/scrape-all-works
func (h *BackgroundTaskHandler) SubmitScrapeAllWorksTask(c *gin.Context) {
	authClaims := middleware.GetUserClaims(c)

	var req task_dto.ScrapeAllWorksTaskPayload
	if err := c.ShouldBindJSON(&req); err != nil {
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid request payload: "+err.Error())
		return
	}
	// payload variable is now directly req
	task, err := h.service.SubmitTask(c.Request.Context(), models.TaskTypeScrapeAllWorks, req, authClaims)
	if err != nil {
		h.logger.ErrorContext(c.Request.Context(), "Failed to submit scrape all works task", "error", err)
		common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to submit scrape all works task: "+err.Error())
		return
	}
	common.SendSuccessResponse(c, http.StatusAccepted, mapBackgroundTaskToDTO(task))
}

// SubmitScrapeSingleWorkTask handles POST /api/v1/admin/tasks/scrape-single-work
func (h *BackgroundTaskHandler) SubmitScrapeSingleWorkTask(c *gin.Context) {
	authClaims := middleware.GetUserClaims(c)

	var req task_dto.ScrapeSingleWorkTaskPayload
	if err := c.ShouldBindJSON(&req); err != nil {
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid request payload: "+err.Error())
		return
	}
	// payload variable is now directly req
	task, err := h.service.SubmitTask(c.Request.Context(), models.TaskTypeScrapeSingleWork, req, authClaims)
	if err != nil {
		h.logger.ErrorContext(c.Request.Context(), "Failed to submit scrape single work task", "work_id", req.WorkID, "error", err)
		common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to submit scrape single work task: "+err.Error())
		return
	}
	common.SendSuccessResponse(c, http.StatusAccepted, mapBackgroundTaskToDTO(task))
}

// GetTaskStatusByID handles GET /api/v1/admin/tasks/:taskID
func (h *BackgroundTaskHandler) GetTaskStatusByID(c *gin.Context) {
	taskID := c.Param("taskID")
	if taskID == "" {
		common.SendErrorResponse(c, http.StatusBadRequest, "Task ID cannot be empty")
		return
	}
	task, err := h.service.GetTaskByID(c.Request.Context(), taskID)
	if err != nil {
		if errors.Is(err, apperrors.ErrTaskNotFound) {
			common.SendErrorResponse(c, http.StatusNotFound, "Task not found")
		} else {
			h.logger.ErrorContext(c.Request.Context(), "Failed to get task status", "task_id", taskID, "error", err)
			common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to retrieve task status")
		}
		return
	}
	common.SendSuccessResponse(c, http.StatusOK, mapBackgroundTaskToDTO(task))
}

// ListTasks handles GET /api/v1/admin/tasks
func (h *BackgroundTaskHandler) ListTasks(c *gin.Context) {
	var params database.ListBackgroundTaskParams
	if err := c.ShouldBindQuery(&params); err != nil {
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid query parameters: "+err.Error())
		return
	}

	// Check if status param exists and convert it to the correct TaskStatus type
	if statusParam := c.Query("status"); statusParam != "" {
		params.Status = models.TaskStatus(statusParam)
	}

	tasks, total, err := h.service.ListTasks(c.Request.Context(), params)
	if err != nil {
		h.logger.ErrorContext(c.Request.Context(), "Failed to list tasks", "params", fmt.Sprintf("%+v", params), "error", err)
		common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to retrieve tasks")
		return
	}
	taskDTOs := make([]*task_dto.BackgroundTaskResponseDTO, len(tasks))
	for i, t := range tasks {
		taskDTOs[i] = mapBackgroundTaskToDTO(t)
	}
	common.SendPaginatedResponse(c, http.StatusOK, taskDTOs, total, params.Page, params.PageSize)
}

// CancelTask handles POST /api/v1/admin/tasks/:taskID/cancel
func (h *BackgroundTaskHandler) CancelTask(c *gin.Context) {
	taskID := c.Param("taskID")
	if taskID == "" {
		common.SendErrorResponse(c, http.StatusBadRequest, "Task ID cannot be empty")
		return
	}

	authClaims := middleware.GetUserClaims(c)
	if authClaims == nil {
		common.SendErrorResponse(c, http.StatusUnauthorized, "Unauthorized: Missing user claims")
		return
	}

	err := h.service.CancelTask(c.Request.Context(), taskID, authClaims)
	if err != nil {
		h.logger.ErrorContext(c.Request.Context(), "Failed to cancel task", "task_id", taskID, "user_id", authClaims.UserID, "error", err)
		if errors.Is(err, apperrors.ErrTaskNotFound) {
			common.SendErrorResponse(c, http.StatusNotFound, "Task not found")
		} else if errors.Is(err, apperrors.ErrTaskCannotBeCancelled) {
			common.SendErrorResponse(c, http.StatusConflict, err.Error())
		} else if errors.Is(err, apperrors.ErrTaskActionForbidden) {
			common.SendErrorResponse(c, http.StatusForbidden, "Forbidden: You are not authorized to cancel this task.")
		} else {
			common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to cancel task: "+err.Error())
		}
		return
	}

	common.SendSuccessResponse(c, http.StatusOK, gin.H{"message": fmt.Sprintf("Task %s cancellation request accepted.", taskID)})
}
