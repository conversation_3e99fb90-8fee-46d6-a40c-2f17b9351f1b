package handler

import (
	"context"
	"errors"
	"fmt"
	"log/slog"
	"net/http"
	"strconv"

	"github.com/Sakura-Byte/kikoeru-go/pkg/apperrors"
	playhistory_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/playhistory" // Added DTO import
	work_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/work"               // Added for WorkDTO in PlayHistoryResponseDTO
	"github.com/Sakura-Byte/kikoeru-go/pkg/http/middleware"
	"github.com/Sakura-Byte/kikoeru-go/pkg/http/rest/common"
	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
	"github.com/Sakura-Byte/kikoeru-go/pkg/ports" // Import ports package
	"github.com/gin-gonic/gin"
)

type PlayHistoryHandler struct {
	service     ports.PlayHistoryService // Changed to ports.PlayHistoryService
	workService ports.WorkService        // Added WorkService dependency
	logger      *slog.Logger
}

func NewPlayHistoryHandler(service ports.PlayHistoryService, workService ports.WorkService, logger *slog.Logger) *PlayHistoryHandler { // Changed to ports.PlayHistoryService
	return &PlayHistoryHandler{
		service:     service,
		workService: workService,
		logger:      logger.With("handler", "PlayHistoryHandler"),
	}
}

// RecordPlayPositionRequest is now defined in playhistory_dto package

// RecordPlayPosition handles POST /api/v1/me/history
func (h *PlayHistoryHandler) RecordPlayPosition(c *gin.Context) {
	userClaims := middleware.GetUserClaims(c)
	if userClaims == nil {
		common.SendErrorResponse(c, http.StatusUnauthorized, "Authentication required: User claims not found.")
		return
	}
	if userClaims.UserID == "guest" || userClaims.UserGroup == models.UserGroupGuest {
		common.SendErrorResponse(c, http.StatusForbidden, "Login required to record play history.")
		return
	}
	username := userClaims.Username
	if username == "" {
		common.SendErrorResponse(c, http.StatusForbidden, "Invalid user session.")
		return
	}

	var req playhistory_dto.RecordPlayPositionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WarnContext(c.Request.Context(), "Failed to bind JSON for RecordPlayPosition", "error", err)
		common.SendErrorResponse(c, http.StatusBadRequest, fmt.Sprintf("Invalid request payload: %v", err))
		return
	}

	err := h.service.RecordPlayPosition(c.Request.Context(), username, req)

	if err != nil {
		if errors.Is(err, apperrors.ErrWorkNotFound) {
			common.SendErrorResponse(c, http.StatusNotFound, err.Error())
		} else if errors.Is(err, apperrors.ErrInvalidPlayHistoryInput) {
			common.SendErrorResponse(c, http.StatusBadRequest, err.Error())
		} else {
			h.logger.ErrorContext(c.Request.Context(), "Failed to record play position", "username", username, "work_id", req.WorkID, "error", err)
			common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to record play position")
		}
		return
	}
	common.SendSuccessResponse(c, http.StatusOK, gin.H{"message": "Play position recorded successfully"})
}

// GetUserPlayHistoryRequest represents the request parameters for listing user play history.
type GetUserPlayHistoryRequest struct {
	Page     int    `form:"page" binding:"omitempty,min=1"`
	PageSize int    `form:"page_size" binding:"omitempty,min=1,max=100"`
	Order    string `form:"order" binding:"omitempty"`
	Sort     string `form:"sort" binding:"omitempty,oneof=asc desc"`
}

func (h *PlayHistoryHandler) mapPlayHistoryToDTO(ctx context.Context, phModel *models.PlayHistory) *playhistory_dto.PlayHistoryResponseDTO {
	if phModel == nil {
		return nil
	}

	var workDTO *work_dto.WorkDTO
	if phModel.Work != nil && phModel.Work.OriginalID != nil && *phModel.Work.OriginalID != "" {
		retrievedWorkDTO, err := h.workService.GetWorkInfoByOriginalID(ctx, *phModel.Work.OriginalID)
		if err != nil {
			h.logger.ErrorContext(ctx, "mapPlayHistoryToDTO: Failed to get WorkDTO for PlayHistory", "work_original_id", *phModel.Work.OriginalID, "play_history_id", phModel.ID, "error", err)
			// Depending on requirements, you might still want to return the play history part
			// or make workDTO nil. For now, workDTO remains nil on error.
		} else {
			workDTO = retrievedWorkDTO
		}
	} else if phModel.Work != nil {
		// This case implies Work model is present but OriginalID is missing.
		// This might happen if Work was not fully preloaded or if OriginalID is optional.
		// If WorkID is always present, we could try fetching by WorkID, but that might be less efficient or not always desired.
		// For now, log a warning if this potentially incomplete state is encountered.
		h.logger.WarnContext(ctx, "mapPlayHistoryToDTO: PlayHistory has associated Work model but OriginalID is nil. WorkDTO will be incomplete or nil.", "play_history_id", phModel.ID, "work_id_on_model", phModel.WorkID)
		// Optionally, you could attempt a partial mapping from phModel.Work if some basic fields are needed and available.
		// workDTO = &work_dto.WorkDTO{ ID: phModel.Work.ID, Title: phModel.Work.Title, ... } // if phModel.Work is guaranteed non-nil
	}

	return &playhistory_dto.PlayHistoryResponseDTO{
		ID:                      phModel.ID,
		CreatedAt:               phModel.CreatedAt,
		UpdatedAt:               phModel.UpdatedAt,
		UserID:                  phModel.UserID,
		TrackPath:               phModel.TrackPath,
		PlaybackPositionSeconds: phModel.PlaybackPositionSeconds,
		ProgressPercentage:      phModel.ProgressPercentage,
		IsFinished:              phModel.IsFinished,
		Work:                    workDTO,
	}
}

// GetUserPlayHistory handles GET /api/v1/me/history
func (h *PlayHistoryHandler) GetUserPlayHistory(c *gin.Context) {
	userClaims := middleware.GetUserClaims(c)
	if userClaims == nil {
		common.SendErrorResponse(c, http.StatusUnauthorized, "Authentication required: User claims not found.")
		return
	}
	if userClaims.UserID == "guest" || userClaims.UserGroup == models.UserGroupGuest {
		common.SendErrorResponse(c, http.StatusForbidden, "Login required to view play history.")
		return
	}
	username := userClaims.Username
	if username == "" {
		common.SendErrorResponse(c, http.StatusForbidden, "Invalid user session.")
		return
	}

	var params GetUserPlayHistoryRequest
	if err := c.ShouldBindQuery(&params); err != nil {
		h.logger.WarnContext(c.Request.Context(), "Failed to bind query parameters for GetUserPlayHistory", "error", err)
		common.SendErrorResponse(c, http.StatusBadRequest, fmt.Sprintf("Invalid query parameters: %v", err))
		return
	}

	// Set default sorting if not specified
	if params.Order == "" {
		params.Order = "updated_at"
	}
	if params.Sort == "" {
		params.Sort = "desc"
	}

	records, totalCount, err := h.service.GetUserPlayHistory(c.Request.Context(), username, params.Page, params.PageSize, params.Order, params.Sort)
	if err != nil {
		h.logger.ErrorContext(c.Request.Context(), "Failed to get user play history", "username", username, "error", err)
		common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to retrieve play history")
		return
	}
	responseDTOs := make([]*playhistory_dto.PlayHistoryResponseDTO, len(records))
	for i, record := range records {
		responseDTOs[i] = h.mapPlayHistoryToDTO(c.Request.Context(), record)
	}
	common.SendPaginatedResponse(c, http.StatusOK, responseDTOs, totalCount, params.Page, params.PageSize)
}

// DeletePlayHistoryEntry handles DELETE /api/v1/me/history/:historyID
func (h *PlayHistoryHandler) DeletePlayHistoryEntry(c *gin.Context) {
	userClaims := middleware.GetUserClaims(c)
	if userClaims == nil {
		common.SendErrorResponse(c, http.StatusUnauthorized, "Authentication required: User claims not found.")
		return
	}
	if userClaims.UserID == "guest" || userClaims.UserGroup == models.UserGroupGuest {
		common.SendErrorResponse(c, http.StatusForbidden, "Login required to delete play history.")
		return
	}
	username := userClaims.Username
	if username == "" {
		common.SendErrorResponse(c, http.StatusForbidden, "Invalid user session.")
		return
	}

	historyIDStr := c.Param("historyID")
	historyID, err := strconv.ParseUint(historyIDStr, 10, 32)
	if err != nil {
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid history ID format")
		return
	}

	err = h.service.DeletePlayHistory(c.Request.Context(), username, uint(historyID))
	if err != nil {
		if errors.Is(err, apperrors.ErrPlayHistoryNotFound) {
			common.SendErrorResponse(c, http.StatusNotFound, err.Error())
		} else if errors.Is(err, apperrors.ErrInvalidPlayHistoryInput) {
			common.SendErrorResponse(c, http.StatusBadRequest, err.Error())
		} else {
			h.logger.ErrorContext(c.Request.Context(), "Failed to delete play history entry", "username", username, "history_id", historyID, "error", err)
			common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to delete play history entry")
		}
		return
	}
	common.SendSuccessResponse(c, http.StatusOK, gin.H{"message": "Play history entry deleted successfully"})
}
