package driver

import (
	"context"
	"fmt"
	"log/slog"
	"path/filepath"
	"strings"
	"sync"
	"time"
)

// DirectoryNode represents a node in the directory tree model
type DirectoryNode struct {
	ID       string
	Name     string
	Path     string
	IsDir    bool
	Children map[string]*DirectoryNode // map[name]*DirectoryNode
	Modified time.Time
	mutex    sync.RWMutex
	// Store metadata from original object to avoid unnecessary fetches
	Size       int64
	ModTime    time.Time
	CreatTime  time.Time
	Hash       AlistHashInfo
	HasFetched bool // Flag to indicate if we've fetched the full object details
}

// DirectoryTreeModel represents a cached model of the directory structure
type DirectoryTreeModel struct {
	rootNode    *DirectoryNode
	storageID   uint
	logger      *slog.Logger
	driver      StorageDriver
	expiration  time.Duration
	initialized bool
	mutex       sync.RWMutex
}

// NewDirectoryTreeModel creates a new directory tree model for a storage driver
func NewDirectoryTreeModel(storageID uint, driver StorageDriver, logger *slog.Logger) *DirectoryTreeModel {
	storage := driver.GetStorage()
	expiration := time.Duration(storage.CacheExpiration) * time.Minute
	if storage.CacheExpiration <= 0 {
		expiration = 5 * time.Minute
	}

	isIDBased := false
	_, ok := driver.(IRootIDGetter)
	if ok {
		isIDBased = true
	}

	var rootID string
	if isIDBased {
		rootIDGetter := driver.(IRootIDGetter)
		rootID = rootIDGetter.GetRootID()
	} else {
		rootID = "/"
	}

	return &DirectoryTreeModel{
		rootNode: &DirectoryNode{
			ID:       rootID,
			Name:     "",
			Path:     "/",
			IsDir:    true,
			Children: make(map[string]*DirectoryNode),
			Modified: time.Now(),
		},
		storageID:   storageID,
		logger:      logger.With("module", "DirectoryTreeModel", "storage_id", storageID),
		driver:      driver,
		expiration:  expiration,
		initialized: false,
	}
}

// Initialize lazily initializes the root node by fetching its contents
func (dtm *DirectoryTreeModel) Initialize(ctx context.Context) error {
	dtm.mutex.Lock()
	defer dtm.mutex.Unlock()

	if dtm.initialized {
		return nil
	}

	dtm.logger.InfoContext(ctx, "Initializing directory tree model", "storage_id", dtm.storageID)

	// Create AlistObj for the root
	rootObj := NewIdAlistObj(dtm.rootNode.ID, dtm.rootNode.Path)

	// List contents of root directory
	items, err := dtm.driver.List(ctx, rootObj, AlistListArgs{})
	if err != nil {
		dtm.logger.ErrorContext(ctx, "Failed to list root directory during initialization",
			"error", err)
		return err
	}

	// Add children to root node
	for _, item := range items {
		childNode := &DirectoryNode{
			ID:         item.GetID(),
			Name:       item.GetName(),
			Path:       filepath.Join(dtm.rootNode.Path, item.GetName()),
			IsDir:      item.IsDir(),
			Children:   make(map[string]*DirectoryNode),
			Modified:   time.Now(),
			Size:       item.GetSize(),
			ModTime:    item.ModTime(),
			CreatTime:  item.CreateTime(),
			Hash:       item.GetHash(),
			HasFetched: false,
		}
		dtm.rootNode.Children[childNode.Name] = childNode

		// Prefetch the first level of directories to reduce future API calls
		if childNode.IsDir {
			go func(dirNode *DirectoryNode) {
				prefetchCtx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
				defer cancel()
				dtm.prefetchDirectoryContents(prefetchCtx, dirNode)
			}(childNode)
		}
	}

	dtm.rootNode.Modified = time.Now()
	dtm.initialized = true
	dtm.logger.InfoContext(ctx, "Directory tree model initialized", "child_count", len(dtm.rootNode.Children))
	return nil
}

// prefetchDirectoryContents fetches the contents of a directory node in the background
// to populate the directory structure and reduce future API calls
func (dtm *DirectoryTreeModel) prefetchDirectoryContents(ctx context.Context, node *DirectoryNode) {
	if !node.IsDir {
		return
	}

	// Skip if already fetched recently
	node.mutex.RLock()
	if time.Since(node.Modified) < dtm.expiration/2 {
		node.mutex.RUnlock()
		return
	}
	node.mutex.RUnlock()

	// Try to refresh this node
	err := dtm.refreshNode(ctx, node)
	if err != nil {
		dtm.logger.DebugContext(ctx, "Failed to prefetch directory contents",
			"path", node.Path,
			"error", err)
	}
}

// ResolvePathToNode resolves a path to a DirectoryNode, populating the tree as needed
func (dtm *DirectoryTreeModel) ResolvePathToNode(ctx context.Context, path string) (*DirectoryNode, error) {
	// Ensure initialized
	if !dtm.initialized {
		if err := dtm.Initialize(ctx); err != nil {
			return nil, err
		}
	}

	// Clean the path
	path = filepath.ToSlash(filepath.Clean(path))
	if path == "." {
		path = "/"
	}

	// Root path is special case
	if path == "/" {
		dtm.rootNode.mutex.RLock()
		defer dtm.rootNode.mutex.RUnlock()
		return dtm.rootNode, nil
	}

	// Split path into components
	parts := strings.Split(path, "/")
	var cleanParts []string
	for _, part := range parts {
		if part != "" {
			cleanParts = append(cleanParts, part)
		}
	}

	// Try to resolve the entire path at once via direct ID lookup if the driver supports it
	if getter, ok := dtm.driver.(Getter); ok {
		// Try to get the object directly by path
		obj, err := getter.Get(ctx, path)
		if err == nil {
			// Successfully got the object directly - create or update a node for it
			dtm.mutex.Lock()
			defer dtm.mutex.Unlock()

			// Create a node for the object
			node := &DirectoryNode{
				ID:         obj.GetID(),
				Name:       obj.GetName(),
				Path:       path,
				IsDir:      obj.IsDir(),
				Children:   make(map[string]*DirectoryNode),
				Modified:   time.Now(),
				Size:       obj.GetSize(),
				ModTime:    obj.ModTime(),
				CreatTime:  obj.CreateTime(),
				Hash:       obj.GetHash(),
				HasFetched: true,
			}

			// Add this node to the directory model at the appropriate place
			dtm.addNodeToTreeByPath(node, cleanParts)

			// Return a copy of the node to avoid locking issues
			return node, nil
		}

		// If direct lookup failed, fall back to path traversal
		dtm.logger.DebugContext(ctx, "Direct path resolution failed, falling back to traversal",
			"path", path,
			"error", err)
	}

	// Start at root
	currentNode := dtm.rootNode
	currentPath := "/"

	for i, part := range cleanParts {
		currentNode.mutex.RLock()

		// Check if node is expired
		if time.Since(currentNode.Modified) > dtm.expiration {
			// We need to refresh this node
			currentNode.mutex.RUnlock()
			if err := dtm.refreshNode(ctx, currentNode); err != nil {
				return nil, err
			}
			currentNode.mutex.RLock()
		}

		child, exists := currentNode.Children[part]
		if !exists {
			// Child doesn't exist in our model, need to refresh
			currentNode.mutex.RUnlock()
			if err := dtm.refreshNode(ctx, currentNode); err != nil {
				return nil, err
			}

			// Try again after refresh
			currentNode.mutex.RLock()
			child, exists = currentNode.Children[part]
			if !exists {
				currentNode.mutex.RUnlock()
				return nil, fmt.Errorf("path component %s not found in %s: %w", part, currentPath, ErrFileNotFound)
			}
		}

		// Move to next node
		nextNode := child
		currentNode.mutex.RUnlock()

		currentNode = nextNode
		currentPath = filepath.Join(currentPath, part)

		// If we're at a non-directory and it's not the final part, fail
		if !currentNode.IsDir && i < len(cleanParts)-1 {
			return nil, fmt.Errorf("path component %s is not a directory: %w", currentPath, ErrNotADirectory)
		}

		// If this is not the last component and it's a directory, prefetch its contents
		// to help with future calls
		if currentNode.IsDir && i < len(cleanParts)-1 {
			go dtm.prefetchDirectoryContents(context.Background(), currentNode)
		}
	}

	return currentNode, nil
}

// addNodeToTreeByPath adds a node to the directory tree at the specified path
// This method is not thread-safe and should be called with a lock held
func (dtm *DirectoryTreeModel) addNodeToTreeByPath(node *DirectoryNode, pathParts []string) {
	if len(pathParts) == 0 {
		return
	}

	currentNode := dtm.rootNode
	currentPath := "/"

	// Traverse to the parent directory
	for i := 0; i < len(pathParts)-1; i++ {
		part := pathParts[i]
		nextPath := filepath.Join(currentPath, part)

		// Check if this part exists
		child, exists := currentNode.Children[part]
		if !exists {
			// Create a placeholder node
			child = &DirectoryNode{
				Name:     part,
				Path:     nextPath,
				IsDir:    true, // Must be a directory if it's part of the path
				Children: make(map[string]*DirectoryNode),
				Modified: time.Now(),
			}
			currentNode.Children[part] = child
		}

		currentNode = child
		currentPath = nextPath
	}

	// Add the node itself to its parent
	lastPart := pathParts[len(pathParts)-1]
	currentNode.Children[lastPart] = node
}

// refreshNode updates the contents of a node by fetching from the driver
func (dtm *DirectoryTreeModel) refreshNode(ctx context.Context, node *DirectoryNode) error {
	node.mutex.Lock()
	defer node.mutex.Unlock()

	dtm.logger.DebugContext(ctx, "Refreshing directory node", "path", node.Path)

	// Only refresh directories
	if !node.IsDir {
		return nil
	}

	// Create AlistObj for the node
	nodeObj := NewIdAlistObj(node.ID, node.Path)

	// List contents
	items, err := dtm.driver.List(ctx, nodeObj, AlistListArgs{})
	if err != nil {
		dtm.logger.ErrorContext(ctx, "Failed to refresh directory node",
			"path", node.Path,
			"error", err)
		return err
	}

	// Re-create children map
	newChildren := make(map[string]*DirectoryNode)

	// Add all children from the listing
	for _, item := range items {
		childName := item.GetName()
		childPath := filepath.Join(node.Path, childName)

		// Check if child already exists in our model
		existingChild, exists := node.Children[childName]
		if exists {
			// Update the existing child
			existingChild.ID = item.GetID()
			existingChild.IsDir = item.IsDir()
			existingChild.Modified = time.Now()
			existingChild.Size = item.GetSize()
			existingChild.ModTime = item.ModTime()
			existingChild.CreatTime = item.CreateTime()
			existingChild.Hash = item.GetHash()
			newChildren[childName] = existingChild
		} else {
			// Create new child node
			newChild := &DirectoryNode{
				ID:         item.GetID(),
				Name:       childName,
				Path:       childPath,
				IsDir:      item.IsDir(),
				Children:   make(map[string]*DirectoryNode),
				Modified:   time.Now(),
				Size:       item.GetSize(),
				ModTime:    item.ModTime(),
				CreatTime:  item.CreateTime(),
				Hash:       item.GetHash(),
				HasFetched: false,
			}
			newChildren[childName] = newChild
		}
	}

	// Replace children map
	node.Children = newChildren
	node.Modified = time.Now()
	node.HasFetched = true

	dtm.logger.DebugContext(ctx, "Directory node refreshed",
		"path", node.Path,
		"child_count", len(node.Children))

	return nil
}

// ResolvePathToID resolves a path to an ID using the directory structure model
func (dtm *DirectoryTreeModel) ResolvePathToID(ctx context.Context, path string) (string, error) {
	node, err := dtm.ResolvePathToNode(ctx, path)
	if err != nil {
		return "", err
	}
	return node.ID, nil
}

// globalDirectoryModels keeps track of directory tree models for each storage ID
var (
	globalDirectoryModels     = make(map[uint]*DirectoryTreeModel)
	globalDirectoryModelMutex sync.RWMutex
)

// GetDirectoryTreeModel returns a directory model for the given storage driver, creating it if needed
func GetDirectoryTreeModel(ctx context.Context, storageID uint, driver StorageDriver, logger *slog.Logger) *DirectoryTreeModel {
	globalDirectoryModelMutex.RLock()
	model, exists := globalDirectoryModels[storageID]
	globalDirectoryModelMutex.RUnlock()

	if exists {
		return model
	}

	// Create new model
	globalDirectoryModelMutex.Lock()
	defer globalDirectoryModelMutex.Unlock()

	// Check again in case another goroutine created it while we were waiting
	model, exists = globalDirectoryModels[storageID]
	if exists {
		return model
	}

	model = NewDirectoryTreeModel(storageID, driver, logger)
	globalDirectoryModels[storageID] = model

	// Initialize model in background
	go func() {
		bgCtx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		err := model.Initialize(bgCtx)
		if err != nil {
			logger.ErrorContext(bgCtx, "Failed to initialize directory tree model in background",
				"storage_id", storageID,
				"error", err)
		}
	}()

	return model
}
