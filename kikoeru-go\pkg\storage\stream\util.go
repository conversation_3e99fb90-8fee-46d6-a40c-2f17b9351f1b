package stream

import (
	"context"
	"fmt"
	"io"
	"net/http"

	"github.com/Sakura-Byte/kikoeru-go/pkg/http_range"
	"github.com/Sakura-Byte/kikoeru-go/pkg/log"
	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
	"github.com/Sakura-Byte/kikoeru-go/pkg/utils"
)

// GetRangeReadCloserFromLink creates a RangeReadCloserIF from a Link
func GetRangeReadCloserFromLink(size int64, link *models.Link) (models.RangeReadCloserIF, error) {
	if link.URL == "" {
		return nil, fmt.Errorf("can't create RangeReadCloser since URL is empty in link")
	}
	rangeReaderFunc := func(ctx context.Context, r http_range.Range) (io.ReadCloser, error) {
		requestHeader := ctx.Value("request_header")
		if requestHeader == nil {
			requestHeader = &http.Header{}
		}
		header := ProcessHeader(*(requestHeader.(*http.Header)), link.Header)
		header = http_range.ApplyRangeToHttpHeader(r, header)

		response, err := RequestHttp(ctx, "GET", header, link.URL)
		if err != nil {
			if response == nil {
				return nil, fmt.Errorf("http request failure, err:%s", err)
			}
			return nil, err
		}

		if r.Start == 0 && (r.Length == -1 || r.Length == size) || response.StatusCode == http.StatusPartialContent ||
			checkContentRange(&response.Header, r.Start) {
			return response.Body, nil
		} else if response.StatusCode == http.StatusOK {
			log.WarnContext(ctx, "remote http server not supporting range request, expect low performance!")
			readCloser, err := GetRangedHttpReader(response.Body, r.Start, r.Length)
			if err != nil {
				return nil, err
			}
			return readCloser, nil
		}

		return response.Body, nil
	}
	resultRangeReadCloser := models.RangeReadCloser{RangeReader: rangeReaderFunc}
	return &resultRangeReadCloser, nil
}

// RequestHttp sends an HTTP request
func RequestHttp(ctx context.Context, method string, header http.Header, url string) (*http.Response, error) {
	req, err := http.NewRequestWithContext(ctx, method, url, nil)
	if err != nil {
		return nil, err
	}
	req.Header = header
	return http.DefaultClient.Do(req)
}

// ProcessHeader processes a header
func ProcessHeader(originHeader http.Header, header map[string]string) http.Header {
	result := originHeader.Clone()
	for k, v := range header {
		result.Set(k, v)
	}
	return result
}

// checkContentRange checks if the Content-Range header matches the expected offset
func checkContentRange(header *http.Header, offset int64) bool {
	start, _, err := http_range.ParseContentRange(header.Get("Content-Range"))
	if err != nil {
		log.Warn("exception trying to parse Content-Range, will ignore", "err", err)
	}
	if start == offset {
		return true
	}
	return false
}

// GetRangedHttpReader returns a reader that only reads from the specified range
func GetRangedHttpReader(reader io.ReadCloser, offset int64, length int64) (io.ReadCloser, error) {
	if offset <= 0 && length < 0 {
		return reader, nil
	}

	if offset > 0 {
		// Skip bytes until offset
		_, err := io.CopyN(io.Discard, reader, offset)
		if err != nil {
			reader.Close()
			return nil, err
		}
	}

	if length >= 0 {
		// Limit reader to length
		return &limitedReadCloser{
			Reader: io.LimitReader(reader, length),
			Closer: reader,
		}, nil
	}

	return reader, nil
}

// ReaderWithCtx wraps a reader with a context
type ReaderWithCtx struct {
	io.Reader
	Ctx context.Context
}

// Read implements io.Reader
func (r *ReaderWithCtx) Read(p []byte) (n int, err error) {
	if utils.IsCanceled(r.Ctx) {
		return 0, r.Ctx.Err()
	}
	return r.Reader.Read(p)
}

// Close implements io.Closer
func (r *ReaderWithCtx) Close() error {
	if c, ok := r.Reader.(io.Closer); ok {
		return c.Close()
	}
	return nil
}
