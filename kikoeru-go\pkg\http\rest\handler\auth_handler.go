package handler

import (
	// "database/sql" // Removed unused import
	"errors"
	"fmt"
	"log/slog"
	"net/http"

	// "strings" // Removed unused import
	// "time" // Removed unused import

	"github.com/Sakura-Byte/kikoeru-go/pkg/apperrors"
	// "github.com/Sakura-Byte/kikoeru-go/pkg/auth" // Removed unused import
	"github.com/Sakura-Byte/kikoeru-go/pkg/config"
	user_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/user"

	// "github.com/Sakura-Byte/kikoeru-go/pkg/email" // Removed unused import
	"github.com/Sakura-Byte/kikoeru-go/pkg/http/middleware"
	"github.com/Sakura-Byte/kikoeru-go/pkg/http/rest/common"

	// "github.com/Sakura-Byte/kikoeru-go/pkg/models" // Removed unused import
	"github.com/Sakura-Byte/kikoeru-go/pkg/ports"
	// "github.com/Sakura-Byte/kikoeru-go/pkg/storage/database" // Removed unused import
	"github.com/gin-gonic/gin"
)

type AuthHandler struct {
	service ports.UserService
	cfg     *config.AppConfig
	logger  *slog.Logger
}

func NewAuthHandler(service ports.UserService, cfg *config.AppConfig) *AuthHandler {
	baseLogger := slog.Default()
	return &AuthHandler{
		service: service,
		cfg:     cfg,
		logger:  baseLogger.With("handler", "AuthHandler"),
	}
}

// Register godoc
// @Summary Register a new user
// @Description Register a new user with username, email (optional), and password.
// @Tags auth
// @Accept json
// @Produce json
// @Param user_registration_request body user_dto.UserRegistrationRequest true "User registration details"
// @Success 201 {object} user_dto.UserResponse "User registered successfully"
// @Failure 400 {object} common.ErrorResponse "Invalid request payload or validation error"
// @Failure 409 {object} common.ErrorResponse "Username or email already exists"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /auth/register [post]
func (h *AuthHandler) Register(c *gin.Context) {
	var req user_dto.UserRegistrationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WarnContext(c.Request.Context(), "Failed to bind JSON for registration", "error", err)
		common.SendErrorResponse(c, http.StatusBadRequest, fmt.Sprintf("Invalid request payload: %v", err))
		return
	}

	ipAddress := c.ClientIP()

	user, err := h.service.RegisterUser(c.Request.Context(), req, ipAddress)
	if err != nil {
		if errors.Is(err, apperrors.ErrUserAlreadyExists) || errors.Is(err, apperrors.ErrRegistrationEmailInUse) || errors.Is(err, apperrors.ErrEmailInUseByAnotherAccount) {
			common.SendErrorResponse(c, http.StatusConflict, err.Error())
		} else if errors.Is(err, apperrors.ErrValidation) || errors.Is(err, apperrors.ErrRegistrationDisabled) || errors.Is(err, apperrors.ErrEmailRequired) {
			common.SendErrorResponse(c, http.StatusBadRequest, err.Error())
		} else {
			h.logger.ErrorContext(c.Request.Context(), "Failed to register user", "username", req.Username, "error", err)
			common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to register user: "+err.Error())
		}
		return
	}

	c.JSON(http.StatusCreated, user)
}

// Login godoc
// @Summary Login a user
// @Description Login a user with username/email and password.
// @Tags auth
// @Accept json
// @Produce json
// @Param user_login_request body user_dto.UserLoginRequest true "User login details"
// @Success 200 {object} user_dto.UserLoginResponse "User logged in successfully"
// @Failure 400 {object} common.ErrorResponse "Invalid request payload or validation error"
// @Failure 401 {object} common.ErrorResponse "Invalid credentials or email not verified"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /auth/login [post]
func (h *AuthHandler) Login(c *gin.Context) {
	var req user_dto.UserLoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WarnContext(c.Request.Context(), "Failed to bind JSON for login", "error", err)
		common.SendErrorResponse(c, http.StatusBadRequest, fmt.Sprintf("Invalid request payload: %v", err))
		return
	}

	tokenString, user, err := h.service.LoginUser(c.Request.Context(), req)
	if err != nil {
		if errors.Is(err, apperrors.ErrInvalidCredentials) || errors.Is(err, apperrors.ErrEmailNotVerified) {
			common.SendErrorResponse(c, http.StatusUnauthorized, err.Error())
		} else if errors.Is(err, apperrors.ErrValidation) {
			common.SendErrorResponse(c, http.StatusBadRequest, err.Error())
		} else {
			h.logger.ErrorContext(c.Request.Context(), "Failed to login user", "identifier", req.Identifier, "error", err)
			common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to login user: "+err.Error())
		}
		return
	}

	c.JSON(http.StatusOK, user_dto.UserLoginResponse{Token: tokenString, User: *user})
}

// GetMe godoc
// @Summary Get current user details
// @Description Get details of the currently authenticated user.
// @Tags auth
// @Produce json
// @Success 200 {object} user_dto.UserResponse "Current user details"
// @Failure 401 {object} common.ErrorResponse "Unauthorized"
// @Failure 404 {object} common.ErrorResponse "User not found"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /auth/me [get]
// @Security BearerAuth
func (h *AuthHandler) GetMe(c *gin.Context) {
	userClaims := middleware.GetUserClaims(c)
	if userClaims == nil || userClaims.UserID == "" {
		common.SendErrorResponse(c, http.StatusUnauthorized, "Authentication required: User claims not found.")
		return
	}

	user, err := h.service.GetUserByID(c.Request.Context(), userClaims.UserID)
	if err != nil {
		if errors.Is(err, apperrors.ErrUserNotFound) {
			common.SendErrorResponse(c, http.StatusNotFound, "User not found")
		} else {
			h.logger.ErrorContext(c.Request.Context(), "Failed to get user by ID for GetMe", "user_id", userClaims.UserID, "error", err)
			common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to retrieve user details")
		}
		return
	}

	c.JSON(http.StatusOK, user)
}

// ChangePassword godoc
// @Summary Change user password
// @Description Change the password for the currently authenticated user.
// @Tags auth
// @Accept json
// @Produce json
// @Param change_password_request body user_dto.ChangePasswordRequest true "Change password details"
// @Success 200 {object} common.SuccessResponse "Password changed successfully"
// @Failure 400 {object} common.ErrorResponse "Invalid request payload or validation error"
// @Failure 401 {object} common.ErrorResponse "Unauthorized or invalid credentials"
// @Failure 403 {object} common.ErrorResponse "Forbidden - Guest users cannot change password"
// @Failure 404 {object} common.ErrorResponse "User not found"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /me/password/change [post]
// @Security BearerAuth
func (h *AuthHandler) ChangePassword(c *gin.Context) {
	userClaims := middleware.GetUserClaims(c)
	if userClaims == nil || userClaims.UserID == "" {
		common.SendErrorResponse(c, http.StatusUnauthorized, "Authentication required: User claims not found.")
		return
	}
	username := userClaims.Username

	var req user_dto.ChangePasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WarnContext(c.Request.Context(), "Failed to bind JSON for change password", "username", username, "error", err)
		common.SendErrorResponse(c, http.StatusBadRequest, fmt.Sprintf("Invalid request payload: %v", err))
		return
	}

	err := h.service.ChangeUserPassword(c.Request.Context(), username, req)
	if err != nil {
		if errors.Is(err, apperrors.ErrUserNotFound) {
			common.SendErrorResponse(c, http.StatusNotFound, "User not found")
		} else if errors.Is(err, apperrors.ErrInvalidCredentials) {
			common.SendErrorResponse(c, http.StatusUnauthorized, "Invalid old password")
		} else if errors.Is(err, apperrors.ErrGuestActionNotAllowed) {
			common.SendErrorResponse(c, http.StatusForbidden, "Guest users cannot change password.")
		} else if errors.Is(err, apperrors.ErrValidation) {
			common.SendErrorResponse(c, http.StatusBadRequest, err.Error())
		} else {
			h.logger.ErrorContext(c.Request.Context(), "Failed to change user password", "username", username, "error", err)
			common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to change password: "+err.Error())
		}
		return
	}

	common.SendSuccessResponse(c, http.StatusOK, gin.H{"message": "Password changed successfully"})
}

// RequestLinkEmail godoc
// @Summary Request to link an email address
// @Description Request a verification email to link an email address to the authenticated user's account.
// @Tags auth
// @Accept json
// @Produce json
// @Param request_link_email_request body user_dto.RequestLinkEmailRequest true "Email address to link"
// @Success 200 {object} common.SuccessResponse "Verification email sent"
// @Failure 400 {object} common.ErrorResponse "Invalid request payload or validation error"
// @Failure 401 {object} common.ErrorResponse "Unauthorized"
// @Failure 403 {object} common.ErrorResponse "Forbidden - Guest users cannot link email"
// @Failure 409 {object} common.ErrorResponse "Email already in use or already linked and verified"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /me/email/link [post]
// @Security BearerAuth
func (h *AuthHandler) LinkEmailHandler(c *gin.Context) {
	userClaims := middleware.GetUserClaims(c)
	if userClaims == nil || userClaims.UserID == "" {
		common.SendErrorResponse(c, http.StatusUnauthorized, "Authentication required: User claims not found.")
		return
	}
	username := userClaims.Username

	var req user_dto.RequestLinkEmailRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WarnContext(c.Request.Context(), "Failed to bind JSON for link email", "username", username, "error", err)
		common.SendErrorResponse(c, http.StatusBadRequest, fmt.Sprintf("Invalid request payload: %v", err))
		return
	}

	ipAddress := c.ClientIP()

	err := h.service.RequestLinkEmail(c.Request.Context(), username, req.Email, ipAddress)
	if err != nil {
		if errors.Is(err, apperrors.ErrUserNotFound) {
			common.SendErrorResponse(c, http.StatusNotFound, "User not found")
		} else if errors.Is(err, apperrors.ErrGuestActionNotAllowed) {
			common.SendErrorResponse(c, http.StatusForbidden, "Guest users cannot link email.")
		} else if errors.Is(err, apperrors.ErrEmailInUseByAnotherAccount) || errors.Is(err, apperrors.ErrEmailAlreadyLinkedAndVerified) {
			common.SendErrorResponse(c, http.StatusConflict, err.Error())
		} else if errors.Is(err, apperrors.ErrValidation) {
			common.SendErrorResponse(c, http.StatusBadRequest, err.Error())
		} else {
			h.logger.ErrorContext(c.Request.Context(), "Failed to request link email", "username", username, "email", req.Email, "error", err)
			common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to request email linking: "+err.Error())
		}
		return
	}

	common.SendSuccessResponse(c, http.StatusOK, gin.H{"message": "Verification email sent. Please check your inbox."})
}

// ChangeEmail godoc
// @Summary Request to change an email address
// @Description Request a verification email to change the email address linked to the authenticated user's account.
// @Tags auth
// @Accept json
// @Produce json
// @Param request_link_email_request body user_dto.RequestLinkEmailRequest true "New email address"
// @Success 200 {object} common.SuccessResponse "Verification email sent to new address"
// @Failure 400 {object} common.ErrorResponse "Invalid request payload or validation error"
// @Failure 401 {object} common.ErrorResponse "Unauthorized"
// @Failure 403 {object} common.ErrorResponse "Forbidden - Guest users cannot change email"
// @Failure 409 {object} common.ErrorResponse "New email already in use or already linked and verified"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /me/email/change [post]
// @Security BearerAuth
func (h *AuthHandler) ChangeEmailHandler(c *gin.Context) {
	// This handler is essentially the same as LinkEmailHandler,
	// as the service layer handles the logic of updating the email field.
	h.LinkEmailHandler(c)
}

// VerifyEmail godoc
// @Summary Verify email address
// @Description Verify the user's email address using a token received via email.
// @Tags auth
// @Accept json
// @Produce json
// @Param verify_email_request body user_dto.VerifyEmailRequest true "Verification token"
// @Success 200 {object} common.SuccessResponse "Email verified successfully"
// @Failure 400 {object} common.ErrorResponse "Invalid request payload or validation error"
// @Failure 401 {object} common.ErrorResponse "Invalid or expired token"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /auth/email/verify [post]
func (h *AuthHandler) VerifyEmailHandler(c *gin.Context) {
	var req user_dto.VerifyEmailRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WarnContext(c.Request.Context(), "Failed to bind JSON for verify email", "error", err)
		common.SendErrorResponse(c, http.StatusBadRequest, fmt.Sprintf("Invalid request payload: %v", err))
		return
	}

	err := h.service.VerifyUserEmail(c.Request.Context(), req.Token)
	if err != nil {
		if errors.Is(err, apperrors.ErrInvalidOrExpiredToken) {
			common.SendErrorResponse(c, http.StatusUnauthorized, err.Error())
		} else if errors.Is(err, apperrors.ErrValidation) {
			common.SendErrorResponse(c, http.StatusBadRequest, err.Error())
		} else {
			h.logger.ErrorContext(c.Request.Context(), "Failed to verify email", "error", err)
			common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to verify email: "+err.Error())
		}
		return
	}

	common.SendSuccessResponse(c, http.StatusOK, gin.H{"message": "Email verified successfully"})
}

// UnlinkEmail godoc
// @Summary Unlink email address
// @Description Unlink the email address from the authenticated user's account. Requires password confirmation.
// @Tags auth
// @Accept json
// @Produce json
// @Param unlink_email_request body user_dto.UnlinkEmailRequest true "Password confirmation"
// @Success 200 {object} common.SuccessResponse "Email unlinked successfully"
// @Failure 400 {object} common.ErrorResponse "Invalid request payload or validation error"
// @Failure 401 {object} common.ErrorResponse "Unauthorized or invalid credentials"
// @Failure 403 {object} common.ErrorResponse "Forbidden - Guest users cannot unlink email"
// @Failure 404 {object} common.ErrorResponse "User not found"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /me/email/unlink [post]
// @Security BearerAuth
func (h *AuthHandler) UnlinkEmailHandler(c *gin.Context) {
	userClaims := middleware.GetUserClaims(c)
	if userClaims == nil || userClaims.UserID == "" {
		common.SendErrorResponse(c, http.StatusUnauthorized, "Authentication required: User claims not found.")
		return
	}
	username := userClaims.Username

	var req user_dto.UnlinkEmailRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WarnContext(c.Request.Context(), "Failed to bind JSON for unlink email", "username", username, "error", err)
		common.SendErrorResponse(c, http.StatusBadRequest, fmt.Sprintf("Invalid request payload: %v", err))
		return
	}

	err := h.service.UnlinkUserEmail(c.Request.Context(), username, req.Password)
	if err != nil {
		if errors.Is(err, apperrors.ErrUserNotFound) {
			common.SendErrorResponse(c, http.StatusNotFound, "User not found")
		} else if errors.Is(err, apperrors.ErrInvalidCredentials) {
			common.SendErrorResponse(c, http.StatusUnauthorized, "Invalid password")
		} else if errors.Is(err, apperrors.ErrGuestActionNotAllowed) {
			common.SendErrorResponse(c, http.StatusForbidden, "Guest users cannot unlink email.")
		} else {
			h.logger.ErrorContext(c.Request.Context(), "Failed to unlink email", "username", username, "error", err)
			common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to unlink email: "+err.Error())
		}
		return
	}

	common.SendSuccessResponse(c, http.StatusOK, gin.H{"message": "Email unlinked successfully"})
}

// RequestPasswordReset godoc
// @Summary Request password reset
// @Description Request a password reset email for the user with the given email address.
// @Tags auth
// @Accept json
// @Produce json
// @Param request_password_reset_request body user_dto.RequestPasswordResetRequest true "Email address for password reset"
// @Success 200 {object} common.SuccessResponse "Password reset email sent (if email exists and is verified)"
// @Failure 400 {object} common.ErrorResponse "Invalid request payload or validation error"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /auth/email/request-password-reset [post]
func (h *AuthHandler) RequestPasswordResetHandler(c *gin.Context) {
	var req user_dto.RequestPasswordResetRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WarnContext(c.Request.Context(), "Failed to bind JSON for request password reset", "error", err)
		common.SendErrorResponse(c, http.StatusBadRequest, fmt.Sprintf("Invalid request payload: %v", err))
		return
	}

	ipAddress := c.ClientIP()

	err := h.service.RequestPasswordReset(c.Request.Context(), req.Email, ipAddress)
	if err != nil {
		if errors.Is(err, apperrors.ErrValidation) {
			common.SendErrorResponse(c, http.StatusBadRequest, err.Error())
		} else {
			h.logger.ErrorContext(c.Request.Context(), "Failed to request password reset", "email", req.Email, "error", err)
			common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to request password reset: "+err.Error())
		}
		return
	}

	// Always return 200 OK to avoid leaking information about which emails are registered.
	common.SendSuccessResponse(c, http.StatusOK, gin.H{"message": "If a matching account with a verified email was found, a password reset email has been sent."})
}

// ResetPasswordWithToken godoc
// @Summary Reset password with token
// @Description Reset the user's password using a token received via email.
// @Tags auth
// @Accept json
// @Produce json
// @Param reset_password_request body user_dto.ResetPasswordWithTokenRequest true "Reset token and new password"
// @Success 200 {object} common.SuccessResponse "Password reset successfully"
// @Failure 400 {object} common.ErrorResponse "Invalid request payload or validation error"
// @Failure 401 {object} common.ErrorResponse "Invalid or expired token"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /auth/email/reset-password [post]
func (h *AuthHandler) ResetPasswordWithTokenHandler(c *gin.Context) {
	var req user_dto.ResetPasswordWithTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WarnContext(c.Request.Context(), "Failed to bind JSON for reset password with token", "error", err)
		common.SendErrorResponse(c, http.StatusBadRequest, fmt.Sprintf("Invalid request payload: %v", err))
		return
	}

	err := h.service.ResetPasswordWithToken(c.Request.Context(), req)
	if err != nil {
		if errors.Is(err, apperrors.ErrInvalidOrExpiredToken) {
			common.SendErrorResponse(c, http.StatusUnauthorized, err.Error())
		} else if errors.Is(err, apperrors.ErrValidation) {
			common.SendErrorResponse(c, http.StatusBadRequest, err.Error())
		} else {
			h.logger.ErrorContext(c.Request.Context(), "Failed to reset password with token", "error", err)
			common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to reset password: "+err.Error())
		}
		return
	}

	common.SendSuccessResponse(c, http.StatusOK, gin.H{"message": "Password reset successfully"})
}
