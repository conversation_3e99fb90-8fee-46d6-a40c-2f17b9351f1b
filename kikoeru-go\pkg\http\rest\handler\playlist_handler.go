package handler

import (
	"errors"
	"fmt"
	"log/slog"
	"net/http"
	"strconv"

	"github.com/Sakura-Byte/kikoeru-go/pkg/apperrors" // For checking service errors
	dto_playlist "github.com/Sakura-Byte/kikoeru-go/pkg/dto/playlist"
	"github.com/Sakura-Byte/kikoeru-go/pkg/http/middleware"
	"github.com/Sakura-Byte/kikoeru-go/pkg/http/rest/common"
	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
	"github.com/Sakura-Byte/kikoeru-go/pkg/ports" // Changed from service to ports
	"github.com/gin-gonic/gin"
)

type PlaylistHandler struct {
	service ports.PlaylistService
	logger  *slog.Logger
}

func NewPlaylistHandler(service ports.PlaylistService) *PlaylistHandler {
	baseLogger := slog.Default()
	return &PlaylistHandler{
		service: service,
		logger:  baseLogger.With("handler", "PlaylistHandler"),
	}
}

// CreatePlaylist godoc
// @Summary Create a new playlist
// @Description Creates a new playlist for the authenticated user.
// @Tags playlists
// @Accept json
// @Produce json
// @Param playlist_request body dto_playlist.CreatePlaylistRequest true "Playlist creation request"
// @Success 201 {object} dto_playlist.PlaylistDTO "Successfully created playlist"
// @Failure 400 {object} common.ErrorResponse "Invalid request payload"
// @Failure 401 {object} common.ErrorResponse "Unauthorized"
// @Failure 403 {object} common.ErrorResponse "Forbidden (e.g., guest user)"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /me/playlists [post]
// @Security BearerAuth
func (h *PlaylistHandler) CreatePlaylist(c *gin.Context) {
	userClaims := middleware.GetUserClaims(c)
	if userClaims == nil {
		common.SendErrorResponse(c, http.StatusUnauthorized, "Authentication required: User claims not found.")
		return
	}
	if userClaims.UserID == "guest" || userClaims.UserGroup == models.UserGroupGuest {
		common.SendErrorResponse(c, http.StatusForbidden, "Login required to create playlists.")
		return
	}
	username := userClaims.Username
	if username == "" {
		common.SendErrorResponse(c, http.StatusForbidden, "Invalid user session.")
		return
	}

	var req dto_playlist.CreatePlaylistRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WarnContext(c.Request.Context(), "Failed to bind JSON for CreatePlaylist", "error", err)
		common.SendErrorResponse(c, http.StatusBadRequest, fmt.Sprintf("Invalid request payload: %v", err))
		return
	}

	playlistDTO, err := h.service.CreatePlaylist(c.Request.Context(), username, req) // Expecting PlaylistDTO
	if err != nil {
		h.logger.ErrorContext(c.Request.Context(), "Failed to create playlist", "username", username, "error", err)
		common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to create playlist")
		return
	}
	common.SendSuccessResponse(c, http.StatusCreated, playlistDTO) // Send PlaylistDTO
}

// ListUserPlaylists godoc
// @Summary List user's playlists
// @Description Retrieves a paginated list of playlists for the authenticated user.
// @Tags playlists
// @Produce json
// @Param page query int false "Page number" default(1)
// @Param pageSize query int false "Number of playlists per page" default(20)
// @Success 200 {object} common.PaginatedResponse{data=[]dto_playlist.PlaylistDTO} "A list of user's playlists" // Changed to dto_playlist.PlaylistDTO
// @Failure 401 {object} common.ErrorResponse "Unauthorized"
// @Failure 403 {object} common.ErrorResponse "Forbidden (e.g., guest user)"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /me/playlists [get]
// @Security BearerAuth
func (h *PlaylistHandler) ListUserPlaylists(c *gin.Context) {
	userClaims := middleware.GetUserClaims(c)
	if userClaims == nil {
		common.SendErrorResponse(c, http.StatusUnauthorized, "Authentication required: User claims not found.")
		return
	}
	if userClaims.UserID == "guest" || userClaims.UserGroup == models.UserGroupGuest {
		common.SendErrorResponse(c, http.StatusForbidden, "Login required to list your playlists.")
		return
	}
	username := userClaims.Username
	if username == "" {
		common.SendErrorResponse(c, http.StatusForbidden, "Invalid user session.")
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	playlistDTOs, totalCount, err := h.service.ListUserPlaylists(c.Request.Context(), username, page, pageSize) // Expecting []*dto_playlist.PlaylistDTO
	if err != nil {
		h.logger.ErrorContext(c.Request.Context(), "Failed to list user playlists", "username", username, "error", err)
		common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to retrieve playlists")
		return
	}
	common.SendPaginatedResponse(c, http.StatusOK, playlistDTOs, totalCount, page, pageSize) // Send []*dto_playlist.PlaylistDTO
}

// GetPlaylistDetails godoc
// @Summary Get playlist details
// @Description Retrieves details for a specific playlist, including its items.
// @Tags playlists
// @Produce json
// @Param playlistID path uint true "Playlist ID"
// @Success 200 {object} dto_playlist.PlaylistWithItemsDTO "Playlist details with items" // Already correct
// @Failure 400 {object} common.ErrorResponse "Invalid playlist ID format"
// @Failure 401 {object} common.ErrorResponse "Unauthorized"
// @Failure 403 {object} common.ErrorResponse "Forbidden"
// @Failure 404 {object} common.ErrorResponse "Playlist not found or not accessible"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /playlists/{playlistID} [get]
// @Security BearerAuth
func (h *PlaylistHandler) GetPlaylistDetails(c *gin.Context) {
	playlistIDStr := c.Param("playlistID")
	playlistID, err := strconv.ParseUint(playlistIDStr, 10, 32)
	if err != nil {
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid playlist ID format")
		return
	}

	userClaims := middleware.GetUserClaims(c)
	requestingUsername := ""
	isGuest := false

	if userClaims != nil {
		requestingUsername = userClaims.Username
		isGuest = userClaims.UserID == "guest" || userClaims.UserGroup == models.UserGroupGuest
	} else {
		common.SendErrorResponse(c, http.StatusUnauthorized, "Authentication required to view playlist details.")
		return
	}

	if requestingUsername == "" && !isGuest {
		common.SendErrorResponse(c, http.StatusForbidden, "Invalid user session for viewing playlist.")
		return
	}

	playlistWithItems, err := h.service.GetPlaylistDetails(c.Request.Context(), requestingUsername, uint(playlistID))
	if err != nil {
		if errors.Is(err, apperrors.ErrPlaylistNotFound) || errors.Is(err, apperrors.ErrPlaylistActionForbidden) { // Using apperrors
			common.SendErrorResponse(c, http.StatusNotFound, "Playlist not found or not accessible")
		} else {
			h.logger.ErrorContext(c.Request.Context(), "Failed to get playlist details", "playlist_id", playlistID, "error", err)
			common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to retrieve playlist details")
		}
		return
	}
	common.SendSuccessResponse(c, http.StatusOK, playlistWithItems)
}

// UpdatePlaylistInfo godoc
// @Summary Update playlist information
// @Description Updates the name, description, or visibility of a user's playlist.
// @Tags playlists
// @Accept json
// @Produce json
// @Param playlistID path uint true "Playlist ID"
// @Param playlist_update_request body dto_playlist.UpdatePlaylistInfoRequest true "Playlist update request"
// @Success 200 {object} dto_playlist.PlaylistDTO "Successfully updated playlist" // Changed to dto_playlist.PlaylistDTO
// @Failure 400 {object} common.ErrorResponse "Invalid request payload or playlist ID"
// @Failure 401 {object} common.ErrorResponse "Unauthorized"
// @Failure 403 {object} common.ErrorResponse "Forbidden"
// @Failure 404 {object} common.ErrorResponse "Playlist not found or update forbidden"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /playlists/{playlistID} [put]
// @Security BearerAuth
func (h *PlaylistHandler) UpdatePlaylistInfo(c *gin.Context) {
	userClaims := middleware.GetUserClaims(c)
	if userClaims == nil {
		common.SendErrorResponse(c, http.StatusUnauthorized, "Authentication required: User claims not found.")
		return
	}
	if userClaims.UserID == "guest" || userClaims.UserGroup == models.UserGroupGuest {
		common.SendErrorResponse(c, http.StatusForbidden, "Login required to update playlists.")
		return
	}
	username := userClaims.Username
	if username == "" {
		common.SendErrorResponse(c, http.StatusForbidden, "Invalid user session.")
		return
	}

	playlistIDStr := c.Param("playlistID")
	playlistID, err := strconv.ParseUint(playlistIDStr, 10, 32)
	if err != nil {
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid playlist ID format")
		return
	}

	var req dto_playlist.UpdatePlaylistInfoRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WarnContext(c.Request.Context(), "Failed to bind JSON for UpdatePlaylistInfo", "error", err)
		common.SendErrorResponse(c, http.StatusBadRequest, fmt.Sprintf("Invalid request payload: %v", err))
		return
	}

	updatedPlaylistDTO, err := h.service.UpdatePlaylistInfo(c.Request.Context(), username, uint(playlistID), req) // Expecting PlaylistDTO
	if err != nil {
		if errors.Is(err, apperrors.ErrPlaylistNotFound) || errors.Is(err, apperrors.ErrPlaylistActionForbidden) { // Using apperrors
			common.SendErrorResponse(c, http.StatusNotFound, "Playlist not found or update forbidden")
		} else {
			h.logger.ErrorContext(c.Request.Context(), "Failed to update playlist info", "playlist_id", playlistID, "error", err)
			common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to update playlist")
		}
		return
	}
	common.SendSuccessResponse(c, http.StatusOK, updatedPlaylistDTO) // Send PlaylistDTO
}

// DeletePlaylist godoc
// @Summary Delete a playlist
// @Description Deletes a playlist owned by the authenticated user.
// @Tags playlists
// @Produce json
// @Param playlistID path uint true "Playlist ID"
// @Success 200 {object} common.SuccessResponse "Playlist deleted successfully"
// @Failure 400 {object} common.ErrorResponse "Invalid playlist ID format"
// @Failure 401 {object} common.ErrorResponse "Unauthorized"
// @Failure 403 {object} common.ErrorResponse "Forbidden"
// @Failure 404 {object} common.ErrorResponse "Playlist not found or deletion forbidden"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /playlists/{playlistID} [delete]
// @Security BearerAuth
func (h *PlaylistHandler) DeletePlaylist(c *gin.Context) {
	userClaims := middleware.GetUserClaims(c)
	if userClaims == nil {
		common.SendErrorResponse(c, http.StatusUnauthorized, "Authentication required: User claims not found.")
		return
	}
	if userClaims.UserID == "guest" || userClaims.UserGroup == models.UserGroupGuest {
		common.SendErrorResponse(c, http.StatusForbidden, "Login required to delete playlists.")
		return
	}
	username := userClaims.Username
	if username == "" {
		common.SendErrorResponse(c, http.StatusForbidden, "Invalid user session.")
		return
	}

	playlistIDStr := c.Param("playlistID")
	playlistID, err := strconv.ParseUint(playlistIDStr, 10, 32)
	if err != nil {
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid playlist ID format")
		return
	}

	err = h.service.DeletePlaylist(c.Request.Context(), username, uint(playlistID))
	if err != nil {
		if errors.Is(err, apperrors.ErrPlaylistNotFound) || errors.Is(err, apperrors.ErrPlaylistActionForbidden) { // Using apperrors
			common.SendErrorResponse(c, http.StatusNotFound, "Playlist not found or deletion forbidden")
		} else {
			h.logger.ErrorContext(c.Request.Context(), "Failed to delete playlist", "playlist_id", playlistID, "error", err)
			common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to delete playlist")
		}
		return
	}
	common.SendSuccessResponse(c, http.StatusOK, gin.H{"message": "Playlist deleted successfully"})
}

// AddItemToPlaylist godoc
// @Summary Add an item to a playlist
// @Description Adds a work (or a specific track from a work) to a user's playlist.
// @Tags playlists
// @Accept json
// @Produce json
// @Param playlistID path uint true "Playlist ID"
// @Param playlist_item_request body dto_playlist.AddItemToPlaylistRequest true "Playlist item request"
// @Success 201 {object} dto_playlist.PlaylistItemDTO "Successfully added item to playlist" // Already correct
// @Failure 400 {object} common.ErrorResponse "Invalid request payload, playlist ID, or work ID"
// @Failure 401 {object} common.ErrorResponse "Unauthorized"
// @Failure 403 {object} common.ErrorResponse "Forbidden"
// @Failure 404 {object} common.ErrorResponse "Playlist or Work not found, or action forbidden"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /playlists/{playlistID}/items [post]
// @Security BearerAuth
func (h *PlaylistHandler) AddItemToPlaylist(c *gin.Context) {
	userClaims := middleware.GetUserClaims(c)
	if userClaims == nil {
		common.SendErrorResponse(c, http.StatusUnauthorized, "Authentication required: User claims not found.")
		return
	}
	if userClaims.UserID == "guest" || userClaims.UserGroup == models.UserGroupGuest {
		common.SendErrorResponse(c, http.StatusForbidden, "Login required to add items to playlists.")
		return
	}
	username := userClaims.Username
	if username == "" {
		common.SendErrorResponse(c, http.StatusForbidden, "Invalid user session.")
		return
	}

	playlistIDStr := c.Param("playlistID")
	playlistID, err := strconv.ParseUint(playlistIDStr, 10, 32)
	if err != nil {
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid playlist ID format")
		return
	}

	var req dto_playlist.AddItemToPlaylistRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WarnContext(c.Request.Context(), "Failed to bind JSON for AddItemToPlaylist", "error", err)
		common.SendErrorResponse(c, http.StatusBadRequest, fmt.Sprintf("Invalid request payload: %v", err))
		return
	}

	item, err := h.service.AddItemToPlaylist(c.Request.Context(), username, uint(playlistID), req)
	if err != nil {
		if errors.Is(err, apperrors.ErrPlaylistNotFound) || errors.Is(err, apperrors.ErrPlaylistActionForbidden) { // Using apperrors
			common.SendErrorResponse(c, http.StatusNotFound, "Playlist not found or action forbidden")
		} else if errors.Is(err, apperrors.ErrWorkNotFound) { // Using apperrors
			common.SendErrorResponse(c, http.StatusBadRequest, "Work to add not found")
		} else if errors.Is(err, apperrors.ErrTrackPathNotFoundInWork) { // Using apperrors
			common.SendErrorResponse(c, http.StatusBadRequest, "Track path not found in work")
		} else {
			h.logger.ErrorContext(c.Request.Context(), "Failed to add item to playlist", "playlist_id", playlistID, "error", err)
			common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to add item to playlist")
		}
		return
	}
	common.SendSuccessResponse(c, http.StatusCreated, item)
}

// RemoveItemFromPlaylist godoc
// @Summary Remove an item from a playlist
// @Description Removes an item from a user's playlist.
// @Tags playlists
// @Produce json
// @Param playlistID path uint true "Playlist ID"
// @Param itemID path uint true "Playlist Item ID"
// @Success 200 {object} common.SuccessResponse "Item removed successfully"
// @Failure 400 {object} common.ErrorResponse "Invalid playlist ID or item ID format"
// @Failure 401 {object} common.ErrorResponse "Unauthorized"
// @Failure 403 {object} common.ErrorResponse "Forbidden"
// @Failure 404 {object} common.ErrorResponse "Playlist item not found or action forbidden"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /playlists/{playlistID}/items/{itemID} [delete]
// @Security BearerAuth
func (h *PlaylistHandler) RemoveItemFromPlaylist(c *gin.Context) {
	userClaims := middleware.GetUserClaims(c)
	if userClaims == nil {
		common.SendErrorResponse(c, http.StatusUnauthorized, "Authentication required: User claims not found.")
		return
	}
	if userClaims.UserID == "guest" || userClaims.UserGroup == models.UserGroupGuest {
		common.SendErrorResponse(c, http.StatusForbidden, "Login required to remove items from playlists.")
		return
	}
	username := userClaims.Username
	if username == "" {
		common.SendErrorResponse(c, http.StatusForbidden, "Invalid user session.")
		return
	}

	playlistIDStr := c.Param("playlistID")
	playlistID, err := strconv.ParseUint(playlistIDStr, 10, 32)
	if err != nil {
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid playlist ID format")
		return
	}
	itemIDStr := c.Param("itemID")
	itemID, err := strconv.ParseUint(itemIDStr, 10, 32)
	if err != nil {
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid item ID format")
		return
	}

	err = h.service.RemoveItemFromPlaylist(c.Request.Context(), username, uint(playlistID), uint(itemID))
	if err != nil {
		if errors.Is(err, apperrors.ErrPlaylistItemNotFound) { // Using apperrors
			common.SendErrorResponse(c, http.StatusNotFound, "Playlist item not found")
		} else if errors.Is(err, apperrors.ErrPlaylistActionForbidden) { // Using apperrors
			common.SendErrorResponse(c, http.StatusForbidden, "Action forbidden on this playlist or item")
		} else {
			h.logger.ErrorContext(c.Request.Context(), "Failed to remove item from playlist", "item_id", itemID, "error", err)
			common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to remove item from playlist")
		}
		return
	}
	common.SendSuccessResponse(c, http.StatusOK, gin.H{"message": "Item removed from playlist successfully"})
}

// UpdatePlaylistItemOrders godoc
// @Summary Reorder items in a playlist
// @Description Updates the order of items within a user's playlist.
// @Tags playlists
// @Accept json
// @Produce json
// @Param playlistID path uint true "Playlist ID"
// @Param item_orders_request body dto_playlist.UpdatePlaylistItemOrdersRequest true "Item orders request"
// @Success 200 {object} common.SuccessResponse "Playlist item orders updated successfully"
// @Failure 400 {object} common.ErrorResponse "Invalid request payload or playlist ID"
// @Failure 401 {object} common.ErrorResponse "Unauthorized"
// @Failure 403 {object} common.ErrorResponse "Forbidden"
// @Failure 404 {object} common.ErrorResponse "Playlist not found or action forbidden"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /playlists/{playlistID}/items/order [put]
// @Security BearerAuth
func (h *PlaylistHandler) UpdatePlaylistItemOrders(c *gin.Context) {
	userClaims := middleware.GetUserClaims(c)
	if userClaims == nil {
		common.SendErrorResponse(c, http.StatusUnauthorized, "Authentication required: User claims not found.")
		return
	}
	if userClaims.UserID == "guest" || userClaims.UserGroup == models.UserGroupGuest {
		common.SendErrorResponse(c, http.StatusForbidden, "Login required to reorder playlist items.")
		return
	}
	username := userClaims.Username
	if username == "" {
		common.SendErrorResponse(c, http.StatusForbidden, "Invalid user session.")
		return
	}

	playlistIDStr := c.Param("playlistID")
	playlistID, err := strconv.ParseUint(playlistIDStr, 10, 32)
	if err != nil {
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid playlist ID format")
		return
	}

	var req dto_playlist.UpdatePlaylistItemOrdersRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WarnContext(c.Request.Context(), "Failed to bind JSON for UpdatePlaylistItemOrders", "error", err)
		common.SendErrorResponse(c, http.StatusBadRequest, fmt.Sprintf("Invalid request payload: %v", err))
		return
	}

	err = h.service.UpdatePlaylistItemOrders(c.Request.Context(), username, uint(playlistID), req.ItemOrders)
	if err != nil {
		if errors.Is(err, apperrors.ErrPlaylistNotFound) || errors.Is(err, apperrors.ErrPlaylistActionForbidden) { // Using apperrors
			common.SendErrorResponse(c, http.StatusNotFound, "Playlist not found or action forbidden")
		} else {
			h.logger.ErrorContext(c.Request.Context(), "Failed to update playlist item orders", "playlist_id", playlistID, "error", err)
			common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to update item orders")
		}
		return
	}
	common.SendSuccessResponse(c, http.StatusOK, gin.H{"message": "Playlist item orders updated successfully"})
}
