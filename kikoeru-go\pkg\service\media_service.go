package service

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"image"      // Import image package
	"image/jpeg" // Import image/jpeg package
	_ "image/png"
	"io" // Import io package
	"log/slog"
	"net/http"
	"os"
	"path/filepath"
	"strings"

	"github.com/Sakura-Byte/kikoeru-go/pkg/apperrors"
	"github.com/Sakura-Byte/kikoeru-go/pkg/config"
	"github.com/Sakura-Byte/kikoeru-go/pkg/ports"
	"github.com/Sakura-Byte/kikoeru-go/pkg/storage/database"
	"github.com/Sakura-Byte/kikoeru-go/pkg/storage/driver"
)

// CoverType and its constants have been moved to github.com/Sakura-Byte/kikoeru-go/pkg/ports

// MediaService interface has been moved to github.com/Sakura-Byte/kikoeru-go/pkg/ports

type mediaService struct {
	workRepo          database.WorkRepository
	storageSourceRepo database.StorageSourceRepository
	scraperService    ports.ScraperService
	fileSystemService ports.FileSystemService
	cfg               *config.AppConfig
	logger            *slog.Logger
}

// Ensure mediaService implements ports.MediaService
var _ ports.MediaService = (*mediaService)(nil)

func NewMediaService(
	workRepo database.WorkRepository,
	storageSourceRepo database.StorageSourceRepository,
	scraperService ports.ScraperService,
	fileSystemService ports.FileSystemService,
	cfg *config.AppConfig,
	logger *slog.Logger,
) ports.MediaService {
	return &mediaService{
		workRepo:          workRepo,
		storageSourceRepo: storageSourceRepo,
		scraperService:    scraperService,
		fileSystemService: fileSystemService,
		cfg:               cfg,
		logger:            logger.With("service", "MediaService"),
	}
}

func (s *mediaService) GetMediaStream(ctx context.Context, workID uint, trackRelPathInWork string, clientIP string, rangeHeader string) (rStream io.ReadCloser, rContentType string, rOriginalSize int64, rActualLength int64, rFilename string, rHttpStatus int, rErr error) {
	s.logger.Info("GetMediaStream request", "work_id", workID, "track_relative_path_in_work", trackRelPathInWork, "client_ip", clientIP, "range", rangeHeader)

	work, err := s.workRepo.GetByID(ctx, workID)
	if err != nil {
		if errors.Is(err, apperrors.ErrWorkNotFound) {
			return nil, "", 0, 0, "", http.StatusNotFound, apperrors.ErrWorkNotFound
		}
		s.logger.Error("Failed to get work for media stream", "work_id", workID, "error", err)
		return nil, "", 0, 0, "", http.StatusInternalServerError, fmt.Errorf("failed to retrieve work %d: %w", workID, err)
	}

	storageSourceModel, err := s.storageSourceRepo.GetByID(ctx, work.StorageID)
	if err != nil {
		s.logger.ErrorContext(ctx, "Failed to get storage source model for media stream", "work_id", workID, "storage_id", work.StorageID, "error", err)
		if errors.Is(err, apperrors.ErrStorageNotFound) {
			return nil, "", 0, 0, "", http.StatusInternalServerError, fmt.Errorf("referenced storage source %d not found for work %d: %w", work.StorageID, workID, apperrors.ErrStorageNotFound)
		}
		return nil, "", 0, 0, "", http.StatusInternalServerError, fmt.Errorf("storage source %d for work %d not found: %w", work.StorageID, workID, err)
	}

	fullTrackPath := filepath.ToSlash(filepath.Join(work.PathInStorage, trackRelPathInWork))

	s.logger.Info("Requesting file stream from FileSystemService",
		"storage_id", storageSourceModel.ID,
		"full_track_path", fullTrackPath,
		"range_header", rangeHeader)

	stream, contentType, originalLen, actualLen, filename, httpStatusOut, errFs := s.fileSystemService.GetFileStream(ctx, storageSourceModel.ID, fullTrackPath, rangeHeader)

	if errFs != nil {
		s.logger.Error("FileSystemService.GetFileStream failed", "work_id", workID, "path", fullTrackPath, "error", errFs)

		retStatus := http.StatusInternalServerError
		if httpStatusOut >= 400 {
			retStatus = httpStatusOut
		} else {
			if errors.Is(errFs, apperrors.ErrFileNotFound) || errors.Is(errFs, driver.ErrFileNotFound) {
				retStatus = http.StatusNotFound
			} else if errors.Is(errFs, apperrors.ErrPathIsDir) {
				retStatus = http.StatusBadRequest
			}
		}
		return nil, "", 0, 0, "", retStatus, errFs
	}

	s.logger.Info("Successfully obtained stream from FileSystemService",
		"work_id", workID, "content_type", contentType, "original_length", originalLen, "actual_length", actualLen, "filename", filename, "http_status", httpStatusOut)

	// Return both original size and actual length
	return stream, contentType, originalLen, actualLen, filename, httpStatusOut, nil
}

func (s *mediaService) GetCoverPathByOriginalIDAndFilename(ctx context.Context, originalID string, filename string) (string, error) {
	if s.cfg.Paths.CoversDir == "" {
		s.logger.ErrorContext(ctx, "CoversDir not configured, cannot serve cover")
		return "", apperrors.ErrCoverDirNotConfigured
	}

	if strings.Contains(filename, "..") || strings.ContainsRune(filename, filepath.Separator) || strings.ContainsRune(filename, '/') {
		s.logger.WarnContext(ctx, "Invalid characters in requested cover filename", "original_id", originalID, "filename", filename)
		return "", apperrors.ErrCoverNotFound
	}
	if strings.Contains(originalID, "..") || strings.ContainsRune(originalID, filepath.Separator) || strings.ContainsRune(originalID, '/') {
		s.logger.WarnContext(ctx, "Invalid characters in requested originalID for cover", "original_id", originalID, "filename", filename)
		return "", apperrors.ErrCoverNotFound
	}

	coverPath := filepath.Join(s.cfg.Paths.DataDir, s.cfg.Paths.CoversDir, originalID, filename)

	s.logger.DebugContext(ctx, "Attempting to serve cover image", "original_id", originalID, "filename", filename, "constructed_path", coverPath)

	if _, err := os.Stat(coverPath); os.IsNotExist(err) {
		s.logger.WarnContext(ctx, "Cover image file not found at path", "path", coverPath)
		return "", apperrors.ErrCoverNotFound
	} else if err != nil {
		s.logger.ErrorContext(ctx, "Error stating cover file", "path", coverPath, "error", err)
		return "", fmt.Errorf("error accessing cover file %s: %w", filename, err)
	}

	return coverPath, nil
}

func (s *mediaService) ProcessAndStoreCover(ctx context.Context, originalID string, coverBytes []byte, coverType ports.CoverType) (string, error) {
	if len(coverBytes) == 0 {
		return "", errors.New("cover bytes are empty")
	}
	if originalID == "" {
		return "", errors.New("originalID is empty")
	}
	if s.cfg == nil || s.cfg.Paths.CoversDir == "" {
		s.logger.ErrorContext(ctx, "CoversDir not configured, cannot save cover", "original_id", originalID)
		return "", apperrors.ErrCoverDirNotConfigured
	}

	img, format, decodeErr := image.Decode(bytes.NewReader(coverBytes))
	finalImageData := coverBytes
	convertToJPEG := false

	if decodeErr != nil {
		s.logger.WarnContext(ctx, "Failed to decode downloaded cover image, attempting to save as is if possible", "original_id", originalID, "error", decodeErr)
	} else if format != "jpeg" {
		s.logger.InfoContext(ctx, "Cover is not JPEG, attempting conversion", "original_format", format, "original_id", originalID)
		convertToJPEG = true
	}

	if convertToJPEG && img != nil {
		var jpgBuf bytes.Buffer
		if encodeErr := jpeg.Encode(&jpgBuf, img, &jpeg.Options{Quality: s.cfg.Scanner.CoverJPEGQuality}); encodeErr != nil {
			s.logger.ErrorContext(ctx, "Failed to encode image to JPEG", "original_id", originalID, "error", encodeErr)
		} else {
			finalImageData = jpgBuf.Bytes()
			s.logger.InfoContext(ctx, "Successfully converted cover to JPEG", "original_id", originalID)
		}
	}

	var finalFilename string
	switch coverType {
	case ports.CoverTypeMain:
		finalFilename = fmt.Sprintf("%s_img_main.jpg", originalID)
	case ports.CoverTypeSam:
		finalFilename = fmt.Sprintf("%s_sam.jpg", originalID)
	default:
		s.logger.WarnContext(ctx, "Unknown cover type, using generic filename", "original_id", originalID, "cover_type", coverType)
		finalFilename = fmt.Sprintf("%s_%s.jpg", originalID, strings.ToLower(string(coverType)))
	}

	targetDir := filepath.Join(s.cfg.Paths.DataDir, s.cfg.Paths.CoversDir, originalID)
	localCoverPath := filepath.Join(targetDir, finalFilename)

	if errMkdir := os.MkdirAll(targetDir, 0755); errMkdir != nil {
		s.logger.ErrorContext(ctx, "Failed to create covers subdirectory for work", "original_id", originalID, "path", targetDir, "error", errMkdir)
		return "", fmt.Errorf("creating covers directory %s: %w", targetDir, errMkdir)
	}

	if errWrite := os.WriteFile(localCoverPath, finalImageData, 0644); errWrite != nil {
		s.logger.ErrorContext(ctx, "Failed to save cover image to disk", "original_id", originalID, "path", localCoverPath, "error", errWrite)
		return "", fmt.Errorf("saving cover image to %s: %w", localCoverPath, errWrite)
	}

	s.logger.InfoContext(ctx, "Cover image saved successfully", "original_id", originalID, "type", coverType, "path", localCoverPath)
	return finalFilename, nil
}
