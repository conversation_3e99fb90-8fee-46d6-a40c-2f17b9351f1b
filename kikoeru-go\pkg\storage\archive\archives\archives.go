package archives

import (
	"context"
	"errors"
	"fmt"
	"io"
	"io/fs"
	"os"
	stdpath "path"
	"strings"

	"github.com/Sakura-Byte/kikoeru-go/pkg/apperrors"
	"github.com/Sakura-Byte/kikoeru-go/pkg/log"
	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
	archivefs "github.com/Sakura-Byte/kikoeru-go/pkg/storage/archive/fs"
	"github.com/Sakura-Byte/kikoeru-go/pkg/storage/archive/tool"
	"github.com/Sakura-Byte/kikoeru-go/pkg/storage/stream"
	"github.com/Sakura-Byte/kikoeru-go/pkg/utils"
	"github.com/mholt/archives"
)

// SeekableStreamWrapper extends StreamWithSeek with required methods
type SeekableStreamWrapper struct {
	*models.StreamWithSeek
	Name string
}

func (s *SeekableStreamWrapper) GetSize() int64 {
	return s.Size
}

func (s *SeekableStreamWrapper) GetName() string {
	return s.Name
}

type Archives struct {
}

func (*Archives) AcceptedExtensions() []string {
	return []string{
		".br", ".bz2", ".gz", ".lz4", ".lz", ".sz", ".s2", ".xz", ".zz", ".zst", ".tar", ".7z",
		// Note: .zip is now handled by the dedicated ZIP implementation
	}
}

func (a *Archives) GetMeta(ss *models.StreamWithSeek, args models.ArchiveArgs) (models.ArchiveMeta, error) {
	wrapper := &SeekableStreamWrapper{
		StreamWithSeek: ss,
		Name:           args.Filename,
	}

	originalPos, err := ss.Seek(0, io.SeekCurrent)
	if err != nil {
		return nil, fmt.Errorf("could not get current stream position: %w", err)
	}
	defer func() {
		_, _ = ss.Seek(originalPos, io.SeekStart)
	}()

	archiveReadAtSeeker := &readAtSeekerWrapper{
		reader: stream.ConvertStreamWithSeekToReaderSeeker(ss),
		size:   ss.GetSize(),
	}

	if _, err := archiveReadAtSeeker.Seek(0, io.SeekStart); err != nil {
		return nil, fmt.Errorf("could not reset stream for Identify: %w", err)
	}

	var isEncrypted bool
	var objects []models.Obj

	fsys, err := getFs(wrapper, args)
	if err != nil {
		return nil, err
	}
	err = fs.WalkDir(fsys, ".", func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			return err
		}
		info, err := d.Info()
		if err != nil {
			return err
		}
		if path == "." {
			return nil
		}
		objects = append(objects, &models.Object{
			Name:     info.Name(),
			Path:     path,
			Size:     info.Size(),
			Modified: info.ModTime(),
			IsFolder: info.IsDir(),
		})
		return nil
	})
	if err != nil {
		return nil, filterPassword(err)
	}

	tree := make([]models.ObjTree, len(objects))
	for i, obj := range objects {
		tree[i] = &models.ObjectTree{Object: *obj.(*models.Object)}
	}

	return &models.ArchiveMetaInfo{
		Comment:   "",
		Encrypted: isEncrypted,
		Tree:      tree,
	}, nil
}

func (*Archives) List(ss *models.StreamWithSeek, args models.ArchiveInnerArgs) ([]models.Obj, error) {
	wrapper := &SeekableStreamWrapper{
		StreamWithSeek: ss,
		Name:           args.ArchiveArgs.Filename,
	}

	fsys, err := getFs(wrapper, args.ArchiveArgs)
	if err != nil {
		return nil, err
	}
	innerPath := strings.TrimPrefix(args.InnerPath, "/")
	if innerPath == "" {
		innerPath = "."
	}
	dirEntries, err := fsys.ReadDir(innerPath)
	if err != nil {
		if isEncodingError(err) {
			return nil, errors.New("error walking archive directory '" + innerPath + "': file names in this archive contain special characters that cannot be processed - please try a different archive tool")
		}
		return nil, filterPassword(err)
	}
	return utils.SliceConvert(dirEntries, func(src fs.DirEntry) (models.Obj, error) {
		info, err := src.Info()
		if err != nil {
			return nil, err
		}
		fullPath := stdpath.Join(args.InnerPath, src.Name())
		if args.InnerPath == "." {
			fullPath = src.Name()
		}
		return &models.Object{
			Name:     info.Name(),
			Path:     fullPath,
			Size:     info.Size(),
			Modified: info.ModTime(),
			IsFolder: info.IsDir(),
		}, nil
	})
}

func (*Archives) Extract(ss *models.StreamWithSeek, args models.ArchiveInnerArgs) (io.ReadCloser, int64, error) {
	// Create a wrapper with GetName and GetSize methods
	wrapper := &SeekableStreamWrapper{
		StreamWithSeek: ss,
		Name:           args.ArchiveArgs.Filename, // Use filename from args
	}

	fsys, err := getFs(wrapper, args.ArchiveArgs)
	if err != nil {
		return nil, 0, err
	}
	file, err := fsys.Open(strings.TrimPrefix(args.InnerPath, "/"))
	if err != nil {
		return nil, 0, filterPassword(err)
	}
	stat, err := file.Stat()
	if err != nil {
		return nil, 0, filterPassword(err)
	}
	return file, stat.Size(), nil
}

func (*Archives) Decompress(ss *models.StreamWithSeek, outputPath string, args models.ArchiveInnerArgs, up func(float64)) error {
	// Create a wrapper with GetName and GetSize methods
	wrapper := &SeekableStreamWrapper{
		StreamWithSeek: ss,
		Name:           args.ArchiveArgs.Filename, // Use filename from args
	}

	fsys, err := getFs(wrapper, args.ArchiveArgs)
	if err != nil {
		return err
	}
	isDir := false
	path := strings.TrimPrefix(args.InnerPath, "/")
	if path == "" {
		isDir = true
		path = "."
	} else {
		stat, err := fsys.Stat(path)
		if err != nil {
			return filterPassword(err)
		}
		if stat.IsDir() {
			isDir = true
			outputPath = stdpath.Join(outputPath, stat.Name())
			err = os.Mkdir(outputPath, 0700)
			if err != nil {
				return filterPassword(err)
			}
		}
	}
	if isDir {
		err = fs.WalkDir(fsys, path, func(p string, d fs.DirEntry, err error) error {
			if err != nil {
				return err
			}
			relPath := strings.TrimPrefix(p, path+"/")
			dstPath := stdpath.Join(outputPath, relPath)
			if d.IsDir() {
				err = os.MkdirAll(dstPath, 0700)
			} else {
				dir := stdpath.Dir(dstPath)
				err = decompress(fsys, p, dir, func(_ float64) {})
			}
			return err
		})
	} else {
		err = decompress(fsys, path, outputPath, up)
	}
	return filterPassword(err)
}

// GetFS provides an fs.FS interface for the archive, using the mholt/archives library.
// It leverages the existing internal getFs method.
func (*Archives) GetFS(ss *models.StreamWithSeek, args models.ArchiveArgs) (fs.FS, error) {
	// Create a wrapper with GetName and GetSize methods
	streamWrapper := &SeekableStreamWrapper{
		StreamWithSeek: ss,
		Name:           args.Filename, // Use filename from args
	}

	return getFs(streamWrapper, args)
}

var _ tool.Tool = (*Archives)(nil)

func init() {
	tool.RegisterTool(&Archives{})
}

// Helper functions for archive operations:

// isEncodingError checks if an error is related to character encoding
func isEncodingError(err error) bool {
	if err == nil {
		return false
	}

	errMsg := err.Error()
	return strings.Contains(errMsg, "invalid") &&
		(strings.Contains(errMsg, "argument") ||
			strings.Contains(errMsg, "character") ||
			strings.Contains(errMsg, "encoding") ||
			strings.Contains(errMsg, "text") ||
			strings.Contains(errMsg, "utf") ||
			strings.Contains(errMsg, "unicode"))
}

// getFs returns a filesystem interface for the archive
func getFs(ss *SeekableStreamWrapper, args models.ArchiveArgs) (archivefs.ArchiveFS, error) {
	ctx := context.Background()

	streamReader := stream.ConvertStreamWithSeekToReaderSeeker(ss.StreamWithSeek)
	archiveReadAtSeeker := &readAtSeekerWrapper{
		reader: streamReader,
		size:   ss.GetSize(),
	}

	// Identify the archive format. The seeker's position may change.
	log.Debug("getFs: Identifying archive", "name", ss.GetName(), "password_length", len(args.Password))
	identifiedFormat, _, err := archives.Identify(ctx, ss.GetName(), archiveReadAtSeeker)
	if err != nil {
		_, _ = archiveReadAtSeeker.Seek(0, io.SeekStart) // Attempt to reset seeker on error
		log.Debug("getFs: Failed to identify archive format", "error", err)
		return nil, filterPassword(err)
	}

	log.Debug("getFs: Successfully identified archive format", "format_type", fmt.Sprintf("%T", identifiedFormat))

	// IMPORTANT: Reset the seeker's position to the beginning of the stream
	// as Identify() might have consumed part of it for detection.
	// ArchiveFS will need to read from the start.
	_, err = archiveReadAtSeeker.Seek(0, io.SeekStart)
	if err != nil {
		return nil, fmt.Errorf("failed to reset archive stream after Identify: %w", err)
	}

	extractor, ok := identifiedFormat.(archives.Extractor)
	if !ok {
		return nil, apperrors.ErrUnsupportedArchiveFormat
	}

	// Check if the format supports passwords (like RAR, 7z)
	if passworder, ok := identifiedFormat.(interface{ SetPassword(string) }); ok && args.Password != "" {
		log.Debug("getFs: Setting password for archive format", "format_type", fmt.Sprintf("%T", identifiedFormat))
		passworder.SetPassword(args.Password)
	}

	// Special handling for specific archive types
	switch format := identifiedFormat.(type) {
	case archives.SevenZip:
		// For 7z archives, directly set the Password field - avoids encoding issues
		if args.Password != "" {
			format.Password = args.Password
			extractor = format // Update the extractor with the new format
			log.Debug("getFs: Set 7z password directly in format object")
		}
	}

	// Construct mholt/archives.ArchiveFS using the (potentially modified) extractor
	archFS := &archives.ArchiveFS{
		// Stream expects an io.ReaderAt. io.NewSectionReader returns *io.SectionReader which implements io.ReaderAt.
		Stream:  io.NewSectionReader(archiveReadAtSeeker, 0, ss.GetSize()),
		Format:  extractor, // Use the (potentially encoding-modified) extractor
		Context: ctx,
	}

	// Wrap the mholt/archives.ArchiveFS to implement our internal archivefs.ArchiveFS interface
	return &archiveFSWrapper{FS: archFS}, nil
}

// readAtSeekerWrapper adapts our stream to the mholt/archives requirements
type readAtSeekerWrapper struct {
	reader io.ReadSeeker
	size   int64
}

func (r *readAtSeekerWrapper) Read(p []byte) (n int, err error) {
	return r.reader.Read(p)
}

func (r *readAtSeekerWrapper) ReadAt(p []byte, off int64) (n int, err error) {
	// Save current position
	currentPos, err := r.reader.Seek(0, io.SeekCurrent)
	if err != nil {
		return 0, err
	}

	// Seek to offset
	_, err = r.reader.Seek(off, io.SeekStart)
	if err != nil {
		return 0, err
	}

	// Read data
	n, err = r.reader.Read(p)

	// Restore position
	_, seekErr := r.reader.Seek(currentPos, io.SeekStart)
	if seekErr != nil && err == nil {
		err = seekErr
	}

	return n, err
}

func (r *readAtSeekerWrapper) Seek(offset int64, whence int) (int64, error) {
	return r.reader.Seek(offset, whence)
}

func (r *readAtSeekerWrapper) Size() int64 {
	return r.size
}

// archiveFSWrapper adapts fs.FS to our ArchiveFS interface
type archiveFSWrapper struct {
	fs.FS
}

func (a *archiveFSWrapper) ReadDir(name string) ([]fs.DirEntry, error) {
	return fs.ReadDir(a.FS, name)
}

func (a *archiveFSWrapper) Open(name string) (fs.File, error) {
	return a.FS.Open(name)
}

func (a *archiveFSWrapper) Stat(name string) (fs.FileInfo, error) {
	file, err := a.FS.Open(name)
	if err != nil {
		return nil, err
	}
	defer file.Close()
	return file.Stat()
}

// filterPassword filters errors related to archive passwords
func filterPassword(err error) error {
	// Check if the error is related to password issues
	if err == nil {
		return nil
	}

	errMsg := err.Error()
	log.Debug("filterPassword: analyzing error", "error", err)

	if strings.Contains(errMsg, "password") ||
		strings.Contains(errMsg, "encrypted") ||
		strings.Contains(errMsg, "decrypt") ||
		strings.Contains(errMsg, "authentication failed") {

		log.Debug("filterPassword: detected password-related error")

		// Different archive tools use different error messages for wrong passwords
		if strings.Contains(errMsg, "wrong") ||
			strings.Contains(errMsg, "incorrect") ||
			strings.Contains(errMsg, "invalid") ||
			strings.Contains(errMsg, "bad") ||
			strings.Contains(errMsg, "authentication failed") {
			log.Debug("filterPassword: detected invalid password error")
			return apperrors.ErrInvalidPassword
		}

		log.Debug("filterPassword: detected password required error")
		return apperrors.ErrPasswordRequired
	}
	return err
}

// decompress extracts a file from an archive to a directory
func decompress(fsys fs.FS, path string, outputPath string, up func(float64)) error {
	file, err := fsys.Open(path)
	if err != nil {
		return err
	}
	defer file.Close()

	// Get file info to determine size
	info, err := file.Stat()
	if err != nil {
		return err
	}

	// Create output file
	fileName := stdpath.Base(path)
	outputFile, err := os.Create(stdpath.Join(outputPath, fileName))
	if err != nil {
		return err
	}
	defer outputFile.Close()

	// Copy with progress updates
	return copyWithProgress(outputFile, file, info.Size(), up)
}

// copyWithProgress copies data from src to dst with progress updates
func copyWithProgress(dst io.Writer, src io.Reader, totalSize int64, up func(float64)) error {
	buf := make([]byte, 32*1024) // 32KB buffer
	var written int64

	for {
		nr, er := src.Read(buf)
		if nr > 0 {
			nw, ew := dst.Write(buf[:nr])
			if nw > 0 {
				written += int64(nw)
				if totalSize > 0 {
					up(float64(written) / float64(totalSize) * 100)
				}
			}
			if ew != nil {
				return ew
			}
			if nr != nw {
				return io.ErrShortWrite
			}
		}
		if er != nil {
			if er == io.EOF {
				return nil
			}
			return er
		}
	}
}
