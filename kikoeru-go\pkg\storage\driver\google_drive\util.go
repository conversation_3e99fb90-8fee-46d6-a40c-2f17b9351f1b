package google_drive

import (
	"context"
	"fmt"
	"log/slog"
	"net/http"
	"time"

	kikoerudriver "github.com/Sakura-Byte/kikoeru-go/pkg/storage/driver"
	"github.com/go-resty/resty/v2"
)

// TokenResp is a local equivalent of <PERSON><PERSON>'s base.TokenResp
type TokenResp struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	ExpiresIn    int64  `json:"expires_in"`
}

// ReqCallback is a callback function to customize a resty request.
type ReqCallback func(req *resty.Request)

func (d *GoogleDrive) refreshToken(ctx context.Context) error {
	// d.logger should be initialized by NewDriver.
	// If called before NewDriver (e.g. from Init if NewDriver doesn't call it first),
	// d.logger might be nil. Adding a nil check for robustness, though ideally NewDriver ensures it.
	currentLogger := d.logger
	if currentLogger == nil {
		currentLogger = slog.Default().With("driver", "GoogleDrive", "method", "refreshToken", "warn", "logger_was_nil_at_call")
	}

	url := "https://www.googleapis.com/oauth2/v4/token"
	var resp TokenResp
	var e TokenError

	if d.httpClient == nil {
		currentLogger.WarnContext(ctx, "httpClient is nil in refreshToken, initializing with default timeout")
		d.httpClient = resty.New().SetTimeout(30 * time.Second)
	}

	currentLogger.DebugContext(ctx, "Attempting to refresh Google Drive token")
	res, err := d.httpClient.R().
		SetContext(ctx).
		SetResult(&resp).
		SetError(&e).
		SetFormData(map[string]string{
			"client_id":     d.Addition.ClientID,
			"client_secret": d.Addition.ClientSecret,
			"refresh_token": d.Addition.RefreshToken,
			"grant_type":    "refresh_token",
		}).
		Post(url)

	if err != nil {
		currentLogger.ErrorContext(ctx, "Failed to refresh Google Drive token (request error)", "error", err)
		return err
	}

	currentLogger.DebugContext(ctx, "Google Drive token refresh response", "status_code", res.StatusCode(), "body_preview", string(res.Body()[:min(1024, len(res.Body()))]))
	if e.Error != "" {
		currentLogger.ErrorContext(ctx, "Google Drive token refresh API error", "api_error", e.Error, "description", e.ErrorDescription)
		return fmt.Errorf("google drive token refresh error: %s - %s", e.Error, e.ErrorDescription)
	}
	if resp.AccessToken == "" {
		currentLogger.ErrorContext(ctx, "Google Drive token refresh: got empty access token", "response_body_preview", string(res.Body()[:min(1024, len(res.Body()))]))
		return fmt.Errorf("failed to refresh token, got empty access token")
	}
	d.AccessToken = resp.AccessToken
	currentLogger.InfoContext(ctx, "Google Drive token refreshed successfully")
	return nil
}

func (d *GoogleDrive) request(ctx context.Context, url string, method string, callback ReqCallback, respBody interface{}) ([]byte, int, error) {
	currentLogger := d.logger
	if currentLogger == nil {
		currentLogger = slog.Default().With("driver", "GoogleDrive", "method", "request", "warn", "logger_was_nil_at_call")
	}

	if d.httpClient == nil {
		currentLogger.WarnContext(ctx, "httpClient is nil in request, initializing with default timeout")
		d.httpClient = resty.New().SetTimeout(30 * time.Second)
	}

	req := d.httpClient.R().SetContext(ctx)
	req.SetHeader("Authorization", "Bearer "+d.AccessToken)
	req.SetQueryParam("includeItemsFromAllDrives", "true")
	req.SetQueryParam("supportsAllDrives", "true")

	if callback != nil {
		callback(req)
	}
	if respBody != nil {
		req.SetResult(respBody)
	}

	var apiError Error
	req.SetError(&apiError)

	res, err := req.Execute(method, url)
	if err != nil {
		currentLogger.ErrorContext(ctx, "Google Drive API request execution error", "url", url, "method", method, "error", err)
		return nil, 0, err
	}

	if apiError.Error.Code != 0 {
		currentLogger.WarnContext(ctx, "Google Drive API returned a structured error", "url", url, "method", method, "code", apiError.Error.Code, "message", apiError.Error.Message)
		if apiError.Error.Code == http.StatusUnauthorized {
			currentLogger.InfoContext(ctx, "Google Drive token expired (401), attempting refresh", "url", url)
			errRefresh := d.refreshToken(ctx)
			if errRefresh != nil {
				currentLogger.ErrorContext(ctx, "Failed to refresh token after 401", "original_url", url, "error", errRefresh)
				return nil, apiError.Error.Code, fmt.Errorf("token refresh failed after 401: %w (original API error: %s)", errRefresh, apiError.Error.Message)
			}
			currentLogger.InfoContext(ctx, "Token refreshed, retrying original request", "url", url)
			return d.request(ctx, url, method, callback, respBody)
		}
		if apiError.Error.Code == http.StatusNotFound {
			return nil, apiError.Error.Code, kikoerudriver.ErrFileNotFound
		}
		return nil, apiError.Error.Code, fmt.Errorf("google drive api error (code %d): %s (%v)", apiError.Error.Code, apiError.Error.Message, apiError.Error.Errors)
	}

	if res.IsError() {
		currentLogger.WarnContext(ctx, "Google Drive API request returned HTTP error status", "url", url, "method", method, "status_code", res.StatusCode(), "body_preview", string(res.Body()[:min(1024, len(res.Body()))]))
		if res.StatusCode() == http.StatusNotFound {
			return nil, res.StatusCode(), kikoerudriver.ErrFileNotFound
		}
		return res.Body(), res.StatusCode(), fmt.Errorf("google drive api http error: status %d", res.StatusCode())
	}

	return res.Body(), res.StatusCode(), nil
}

func (d *GoogleDrive) getFiles(ctx context.Context, id string) ([]File, error) {
	currentLogger := d.logger
	if currentLogger == nil {
		currentLogger = slog.Default().With("driver", "GoogleDrive", "method", "getFiles", "warn", "logger_was_nil_at_call")
	}

	pageToken := "first"
	resFiles := make([]File, 0)
	fields := "files(id,name,mimeType,size,modifiedTime,createdTime,thumbnailLink,shortcutDetails),nextPageToken"

	for pageToken != "" {
		select {
		case <-ctx.Done():
			currentLogger.InfoContext(ctx, "getFiles cancelled", "folder_id", id)
			return nil, ctx.Err()
		default:
		}
		if pageToken == "first" {
			pageToken = ""
		}
		var respData Files

		query := map[string]string{
			"orderBy":   "folder,name,modifiedTime desc",
			"fields":    fields,
			"pageSize":  "1000",
			"q":         fmt.Sprintf("'%s' in parents and trashed = false", id),
			"pageToken": pageToken,
		}

		currentLogger.DebugContext(ctx, "Fetching files from Google Drive", "folder_id", id, "page_token", pageToken)
		_, statusCode, err := d.request(ctx, "https://www.googleapis.com/drive/v3/files", http.MethodGet, func(req *resty.Request) {
			req.SetQueryParams(query)
		}, &respData)

		if err != nil {
			currentLogger.ErrorContext(ctx, "Failed to get files page from Google Drive", "folder_id", id, "page_token", pageToken, "status_code", statusCode, "error", err)
			return nil, err
		}

		pageToken = respData.NextPageToken
		resFiles = append(resFiles, respData.Files...)
		currentLogger.DebugContext(ctx, "Fetched page of files", "folder_id", id, "count_on_page", len(respData.Files), "next_page_token", pageToken)
	}
	currentLogger.InfoContext(ctx, "Finished fetching all files for folder", "folder_id", id, "total_files_fetched", len(resFiles))
	return resFiles, nil
}

// min is a helper function to find the minimum of two integers.
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
