package service

import (
	"context"
	"errors"
	"fmt"
	"io"
	"io/fs"
	"mime"
	"net/http"
	"path/filepath"
	"sort"
	"strings"
	"sync"
	"time"

	"github.com/Sakura-Byte/kikoeru-go/pkg/apperrors"
	"github.com/Sakura-Byte/kikoeru-go/pkg/http_range"
	"github.com/Sakura-Byte/kikoeru-go/pkg/log"
	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
	"github.com/Sakura-Byte/kikoeru-go/pkg/ports"

	archiveTool "github.com/Sakura-Byte/kikoeru-go/pkg/storage/archive/tool"
	"github.com/Sakura-Byte/kikoeru-go/pkg/utils"
)

// ArchiveService implements archive-related operations for the FileSystemService
type ArchiveService struct {
	storageService  ports.StorageService
	passwordService *ArchivePasswordService
	listCache       map[string]*listCacheEntry
	treeCache       map[string]*treeCacheEntry
	cacheLock       sync.RWMutex

	// Connection pool for archive handles
	archivePool      map[string]*archiveHandle
	archivePoolLock  sync.RWMutex
	archivePoolClean *time.Timer
}

type archiveHandle struct {
	fs         fs.FS               // The filesystem interface for this archive
	password   string              // The password used for this archive
	lastAccess time.Time           // When this handle was last used
	fileIndex  map[string]fileInfo // Quick lookup for files in this archive
}

type fileInfo struct {
	Path         string    // Path within the archive
	Size         int64     // File size
	IsDir        bool      // Is a directory
	ModifiedTime time.Time // Modification time
}

type listCacheEntry struct {
	entries    []ports.ArchiveEntry
	expiration time.Time // For TTL, if implemented
}

type treeCacheEntry struct {
	node       *ports.ArchiveTreeNode
	expiration time.Time // For TTL, if implemented
}

const cacheTTL = 5 * time.Minute // Example TTL
const poolTTL = 10 * time.Minute // How long to keep unused archive handles

// testPassword checks if a password is valid by attempting to get the archive's metadata.
// It returns the metadata on success, which can be reused to avoid re-reading the archive.
func (s *ArchiveService) testPassword(tool archiveTool.Tool, ss *models.StreamWithSeek, args models.ArchiveArgs) (models.ArchiveMeta, error) {
	// Save the current position to reset the stream after the test
	currentPos, err := ss.Seeker.Seek(0, io.SeekCurrent)
	if err != nil {
		return nil, fmt.Errorf("could not get current stream position for password test: %w", err)
	}
	defer ss.Seeker.Seek(currentPos, io.SeekStart) // Ensure we reset position

	// Reset to beginning for the test
	if _, err := ss.Seeker.Seek(0, io.SeekStart); err != nil {
		return nil, fmt.Errorf("failed to seek to start for password test: %w", err)
	}

	// GetMeta is now the canonical operation for testing a password.
	// For RAR, it will check encryption headers and fail if the password is wrong.
	// For other formats, it may attempt a lightweight operation.
	meta, err := tool.GetMeta(ss, args)
	if err != nil {
		return nil, err
	}

	return meta, nil
}

// tryPasswordsForArchive attempts to find a working password for an archive and returns the metadata.
func (s *ArchiveService) tryPasswordsForArchive(ctx context.Context, storageID uint, archivePath string, providedPassword string, tool archiveTool.Tool, ss *models.StreamWithSeek, args models.ArchiveArgs) (string, models.ArchiveMeta, error) {
	startTime := time.Now()
	var meta models.ArchiveMeta
	var err error

	// Standard path for all archives

	// Fast path: If a password was explicitly provided, try it first
	if providedPassword != "" {
		testArgs := args
		testArgs.Password = providedPassword
		log.Debug(ctx, "Testing provided password", "archivePath", archivePath)
		if meta, err = s.testPassword(tool, ss, testArgs); err == nil {
			// Success! Cache this password for future use
			if s.passwordService != nil {
				s.passwordService.SetCachedPassword(ctx, storageID, archivePath, providedPassword)
			}
			log.Debug(ctx, "Provided password works", "archivePath", archivePath, "time", time.Since(startTime))
			return providedPassword, meta, nil
		} else if isPasswordError(err) {
			// If a password was provided and it's wrong, fail fast.
			log.Debug(ctx, "Provided password is invalid", "archivePath", archivePath, "time", time.Since(startTime))
			return "", nil, apperrors.ErrInvalidPassword
		}
	}

	// Try cached password - this should be very fast with our memory cache
	if s.passwordService != nil {
		cachedPassword, err := s.passwordService.GetCachedPassword(ctx, storageID, archivePath)
		if err == nil && cachedPassword != "" {
			testArgs := args
			testArgs.Password = cachedPassword
			log.Debug(ctx, "Testing cached password", "archivePath", archivePath)
			if meta, err = s.testPassword(tool, ss, testArgs); err == nil {
				log.Debug(ctx, "Cached password works", "archivePath", archivePath, "time", time.Since(startTime))
				return cachedPassword, meta, nil
			}
			// If cached password fails, remove it so we don't try again
			s.passwordService.DeleteCachedPassword(ctx, storageID, archivePath)
		}
	}

	// Try with no password - often faster than trying the password list
	// Saves us from trying the entire list if no password is needed
	log.Debug(ctx, "Testing with no password", "archivePath", archivePath)
	emptyArgs := args
	emptyArgs.Password = ""
	if meta, err = s.testPassword(tool, ss, emptyArgs); err == nil {
		log.Debug(ctx, "No password needed", "archivePath", archivePath, "time", time.Since(startTime))
		return "", meta, nil
	}

	// Try password list as last resort
	if s.passwordService != nil {
		passwordList, err := s.passwordService.GetPasswordList(ctx)
		log.Debug(ctx, "GetPasswordList returned passwords", "count", len(passwordList), "error", err)
		if err == nil && len(passwordList) > 0 {
			log.Debug(ctx, "Testing passwords from list", "count", len(passwordList), "archivePath", archivePath)

			// Try each password until one works
			for _, password := range passwordList {
				testArgs := args
				testArgs.Password = password
				// Reset position for each try
				if _, seekErr := ss.Seeker.Seek(0, io.SeekStart); seekErr != nil {
					continue // Skip this password if seeking fails
				}
				if meta, err = s.testPassword(tool, ss, testArgs); err == nil {
					// Found working password, cache it
					s.passwordService.SetCachedPassword(ctx, storageID, archivePath, password)
					log.Debug(ctx, "Found working password in list", "archivePath", archivePath, "time", time.Since(startTime))
					return password, meta, nil
				}
			}
		}
	}

	log.Debug(ctx, "No working password found", "archivePath", archivePath, "time", time.Since(startTime))
	return "", nil, apperrors.ErrPasswordRequired
}

// isPasswordError checks if an error is related to password issues
func isPasswordError(err error) bool {
	if err == nil {
		return false
	}
	errMsg := strings.ToLower(err.Error())

	// Log the error for debugging
	ctx := context.Background()
	log.Debug(ctx, "Checking error", "error", err)

	// Common password-related error phrases
	passwordErrorPhrases := []string{
		"password",
		"encrypted",
		"decrypt",
		"authentication",
		"authorization",
		"wrong",
		"incorrect",
		"invalid",
		"bad",
		"data error", // Common in RAR with wrong passwords
		"crc failed", // Common in archives with wrong passwords
		"checksum",
		"verification failed",
		"access denied",
	}

	for _, phrase := range passwordErrorPhrases {
		if strings.Contains(errMsg, phrase) {
			log.Debug(ctx, "Detected password-related error", "phrase", phrase)
			return true
		}
	}

	return false
}

// NewArchiveService creates a new ArchiveService
func NewArchiveService(storageService ports.StorageService, passwordService *ArchivePasswordService) *ArchiveService {
	service := &ArchiveService{
		storageService:  storageService,
		passwordService: passwordService,
		listCache:       make(map[string]*listCacheEntry),
		treeCache:       make(map[string]*treeCacheEntry),
		archivePool:     make(map[string]*archiveHandle),
	}

	// Set up a cleanup timer for the archive pool
	service.archivePoolClean = time.AfterFunc(5*time.Minute, service.cleanupArchivePool)

	return service
}

// cleanupArchivePool removes old, unused archive handles from the pool
func (s *ArchiveService) cleanupArchivePool() {
	s.archivePoolLock.Lock()
	defer s.archivePoolLock.Unlock()

	cutoff := time.Now().Add(-poolTTL)
	var toRemove []string

	for key, handle := range s.archivePool {
		if handle.lastAccess.Before(cutoff) {
			toRemove = append(toRemove, key)
		}
	}

	for _, key := range toRemove {
		delete(s.archivePool, key)
	}

	log.Debug(context.Background(), "Cleaned up unused archive handles", "count", len(toRemove))

	// Reset the timer for next cleanup
	s.archivePoolClean.Reset(5 * time.Minute)
}

// Gets or creates an archive handle from the pool
func (s *ArchiveService) getArchiveHandle(ctx context.Context, storageID uint, archivePath string, password string, encoding string) (fs.FS, error) {
	startTime := time.Now()
	poolKey := fmt.Sprintf("%d:%s:%s:%s", storageID, archivePath, password, encoding)

	// Check if we have a cached handle
	s.archivePoolLock.RLock()
	handle, exists := s.archivePool[poolKey]
	s.archivePoolLock.RUnlock()

	if exists {
		// Update last access time
		s.archivePoolLock.Lock()
		handle.lastAccess = time.Now()
		s.archivePoolLock.Unlock()
		log.Debug(ctx, "Using cached handle", "archivePath", archivePath, "time", time.Since(startTime))
		return handle.fs, nil
	}

	// Check if we have a handle with a different password
	// This lets us avoid reopening the archive if we just need a different password
	s.archivePoolLock.RLock()
	for k, existingHandle := range s.archivePool {
		if strings.HasPrefix(k, fmt.Sprintf("%d:%s:", storageID, archivePath)) {
			// Found a handle for the same archive but different password
			s.archivePoolLock.RUnlock()
			log.Debug(ctx, "Found handle with different password", "archivePath", archivePath)
			// Just create a new cache entry with the new password
			// This is much faster than reopening the archive
			newPoolKey := fmt.Sprintf("%d:%s:%s:%s", storageID, archivePath, password, encoding)
			s.archivePoolLock.Lock()
			existingHandle.lastAccess = time.Now()
			s.archivePool[newPoolKey] = existingHandle
			s.archivePoolLock.Unlock()
			return existingHandle.fs, nil
		}
	}
	s.archivePoolLock.RUnlock()

	log.Debug(ctx, "Creating new handle", "archivePath", archivePath)

	// Otherwise, create a new handle
	fileStream, _, err := s.storageService.GetFileStream(ctx, storageID, archivePath)
	if err != nil {
		return nil, err
	}
	// We'll close this later when removing from the pool, or immediately on error

	readSeeker, ok := fileStream.(io.ReadSeeker)
	if !ok {
		fileStream.Close()
		return nil, fmt.Errorf("file stream does not support seeking, required for archives")
	}

	var archiveSize int64
	if seekerSize, ok := fileStream.(interface{ Size() int64 }); ok {
		archiveSize = seekerSize.Size()
	} else if seeker, ok := fileStream.(io.Seeker); ok {
		currentPos, err := seeker.Seek(0, io.SeekCurrent)
		if err != nil {
			fileStream.Close()
			return nil, err
		}
		endPos, err := seeker.Seek(0, io.SeekEnd)
		if err != nil {
			fileStream.Close()
			return nil, err
		}
		_, err = seeker.Seek(currentPos, io.SeekStart)
		if err != nil {
			fileStream.Close()
			return nil, err
		}
		archiveSize = endPos
	} else {
		fileStream.Close()
		return nil, fmt.Errorf("could not determine archive size")
	}

	ss := &models.StreamWithSeek{
		ReadCloser: io.NopCloser(readSeeker),
		Seeker:     readSeeker,
		Size:       archiveSize,
	}

	tool, multipartExt, err := archiveTool.GetArchiveTool(archivePath)
	if err != nil {
		fileStream.Close()
		return nil, err
	}

	// For now, full multipart handling by finding other files is not implemented.
	// We will proceed with the single stream, which works for some multipart formats
	// that can be read from a single file (like some ZIPs).
	if multipartExt != nil {
		log.Debug(ctx, "Multipart archive detected, proceeding with single stream. Full multi-stream support pending.", "archivePath", archivePath)
	}

	// Try to find a working password and get metadata in one go
	workingPassword, meta, err := s.tryPasswordsForArchive(ctx, storageID, archivePath, password, tool, ss, models.ArchiveArgs{
		Filename: archivePath,
		Encoding: encoding,
	})

	if err != nil {
		fileStream.Close()
		return nil, err
	}

	// Reset the stream for filesystem
	if _, err := ss.Seeker.Seek(0, io.SeekStart); err != nil {
		fileStream.Close()
		return nil, err
	}

	// Open the archive filesystem
	args := models.ArchiveArgs{
		Password: workingPassword,
		Filename: archivePath,
		Encoding: encoding,
	}

	log.Debug(ctx, "Opening archive filesystem", "archivePath", archivePath)
	fsOpenStartTime := time.Now()

	archiveFS, err := tool.GetFS(ss, args)
	if err != nil {
		fileStream.Close()
		return nil, err
	}

	log.Debug(ctx, "Opened archive filesystem", "archivePath", archivePath, "time", time.Since(fsOpenStartTime))

	// Build a file index for fast lookups
	fileIndex := make(map[string]fileInfo)
	if meta != nil && meta.GetTree() != nil {
		for _, objTree := range meta.GetTree() {
			obj := objTree.GetObject()
			path := obj.GetPath()
			fileIndex[path] = fileInfo{
				Path:         path,
				Size:         obj.GetSize(),
				IsDir:        obj.IsDir(),
				ModifiedTime: obj.ModTime(),
			}
		}
		log.Debug(ctx, "Built file index", "count", len(fileIndex))
	}

	// Add to pool with the actual working password
	realPoolKey := fmt.Sprintf("%d:%s:%s:%s", storageID, archivePath, workingPassword, encoding)
	newHandle := &archiveHandle{
		fs:         archiveFS,
		password:   workingPassword,
		lastAccess: time.Now(),
		fileIndex:  fileIndex,
	}

	s.archivePoolLock.Lock()
	s.archivePool[realPoolKey] = newHandle

	// If user provided a different password but we found a working one,
	// add an alias entry to avoid reopening the archive.
	// This is key to making the first request succeed when no password is provided
	// but one is found automatically.
	if password != workingPassword {
		s.archivePool[poolKey] = newHandle
		log.Debug(ctx, "Added alias entry for original password key")
	}
	s.archivePoolLock.Unlock()

	log.Debug(ctx, "Created new handle", "archivePath", archivePath, "time", time.Since(startTime))

	return archiveFS, nil
}

// ListArchive lists files and directories in an archive (with caching)
func (s *ArchiveService) ListArchive(ctx context.Context, req ports.ArchiveRequest) ([]ports.ArchiveEntry, error) {
	cacheKey := fmt.Sprintf("list:%d:%s:%s:%s:%s", req.StorageID, req.PathInStorage, req.PathInArchive, req.Password, req.Encoding)
	if !req.Refresh {
		s.cacheLock.RLock()
		cached, found := s.listCache[cacheKey]
		s.cacheLock.RUnlock()
		if found && (cacheTTL == 0 || time.Now().Before(cached.expiration)) { // Check TTL if used
			return cached.entries, nil
		}
	}

	storageObj, err := s.storageService.GetStorageSource(ctx, req.StorageID)
	if err != nil {
		return nil, err
	}
	if !storageObj.Enabled {
		return nil, apperrors.ErrStorageSourceDisabled
	}

	// Try to get archive from our pool first - this is much faster
	archiveFS, err := s.getArchiveHandle(ctx, req.StorageID, req.PathInStorage, req.Password, req.Encoding)
	if err != nil {
		return nil, err
	}

	// Get the archive handle to check if we have a cached file index
	poolKey := fmt.Sprintf("%d:%s:%s:%s", req.StorageID, req.PathInStorage, req.Password, req.Encoding)
	s.archivePoolLock.RLock()
	handle := s.archivePool[poolKey] // Should exist since getArchiveHandle succeeded
	s.archivePoolLock.RUnlock()

	// If we have a file index in the handle, use it for super fast listing
	if handle != nil && len(handle.fileIndex) > 0 {
		pathInArchive := strings.Trim(req.PathInArchive, "/")
		if pathInArchive == "." {
			pathInArchive = ""
		}

		var finalEntries []ports.ArchiveEntry
		for _, info := range handle.fileIndex {
			dir := filepath.ToSlash(filepath.Dir(info.Path))
			if dir == "." {
				dir = ""
			}

			if dir == pathInArchive {
				finalEntries = append(finalEntries, ports.ArchiveEntry{
					Name:         filepath.Base(info.Path),
					IsDir:        info.IsDir,
					Path:         info.Path,
					Size:         info.Size,
					ModifiedTime: info.ModifiedTime,
					Type:         utils.DetermineFileType(filepath.Base(info.Path), info.IsDir),
				})
			}
		}

		// Sort entries alphabetically
		sort.Slice(finalEntries, func(i, j int) bool {
			return finalEntries[i].Name < finalEntries[j].Name
		})

		// Cache the result
		if len(finalEntries) > 0 {
			s.cacheLock.Lock()
			s.listCache[cacheKey] = &listCacheEntry{
				entries:    finalEntries,
				expiration: time.Now().Add(cacheTTL),
			}
			s.cacheLock.Unlock()
		}

		log.Debug(ctx, "Using cached file index", "path", req.PathInStorage, "count", len(finalEntries))
		return finalEntries, nil
	}

	// Fall back to using the standard fs.ReadDir
	log.Debug(ctx, "Falling back to fs.ReadDir", "path", req.PathInStorage)

	innerPath := strings.TrimPrefix(req.PathInArchive, "/")
	if innerPath == "" {
		innerPath = "."
	}
	dirEntries, err := fs.ReadDir(archiveFS, innerPath)
	if err != nil {
		return nil, fmt.Errorf("error reading directory in archive: %w", err)
	}

	actualResult := make([]ports.ArchiveEntry, 0, len(dirEntries))
	for _, entry := range dirEntries {
		info, err := entry.Info()
		if err != nil {
			continue // Skip entries we can't get info for
		}

		fullPath := filepath.Join(req.PathInArchive, entry.Name())
		if req.PathInArchive == "." {
			fullPath = entry.Name()
		}

		actualResult = append(actualResult, ports.ArchiveEntry{
			Name:         entry.Name(),
			IsDir:        entry.IsDir(),
			Path:         fullPath,
			Size:         info.Size(),
			ModifiedTime: info.ModTime(),
			Type:         utils.DetermineFileType(entry.Name(), entry.IsDir()),
		})
	}

	// Cache the result
	s.cacheLock.Lock()
	s.listCache[cacheKey] = &listCacheEntry{
		entries:    actualResult,
		expiration: time.Now().Add(cacheTTL),
	}
	s.cacheLock.Unlock()

	return actualResult, nil
}

func buildTreeFromObjectList(rootPath string, allObjects []models.Obj) (*ports.ArchiveTreeNode, error) {
	// This function will replicate the logic from fs.WalkDir handler, but on an in-memory list.
	// It will use a map to build the tree efficiently.
	var rootObj models.Obj
	// Sanitize rootPath
	rootPath = filepath.ToSlash(strings.Trim(rootPath, "/"))
	if rootPath == "" {
		rootPath = "."
	}

	if rootPath == "." {
		// Virtual root for the whole archive
		rootObj = &models.Object{Name: ".", IsFolder: true, Path: "."}
	} else {
		for _, obj := range allObjects {
			// Find the explicit root object
			if filepath.ToSlash(strings.Trim(obj.GetPath(), "/")) == rootPath {
				rootObj = obj
				break
			}
		}
	}

	if rootObj == nil {
		return nil, apperrors.ErrPathNotFound
	}

	rootEntry := ports.ArchiveEntry{
		Name:         rootObj.GetName(),
		IsDir:        rootObj.IsDir(),
		Path:         rootObj.GetPath(),
		Size:         rootObj.GetSize(),
		ModifiedTime: rootObj.ModTime(),
		Type:         utils.DetermineFileType(rootObj.GetName(), rootObj.IsDir()),
	}

	rootNode := &ports.ArchiveTreeNode{
		Entry:    rootEntry,
		Children: []*ports.ArchiveTreeNode{},
	}

	if !rootObj.IsDir() {
		return rootNode, nil // Root is a file
	}

	nodes := make(map[string]*ports.ArchiveTreeNode)
	nodes[rootObj.GetPath()] = rootNode
	if rootObj.GetPath() == "." {
		nodes[""] = rootNode // Also map empty path to root for easier lookup
	}

	// Sort objects by path to ensure parents are created before children
	sort.Slice(allObjects, func(i, j int) bool {
		return len(allObjects[i].GetPath()) < len(allObjects[j].GetPath())
	})

	for _, obj := range allObjects {
		path := obj.GetPath()
		if path == rootObj.GetPath() {
			continue // Skip root, it's already created
		}

		parentPath := filepath.ToSlash(filepath.Dir(path))

		parentNode, exists := nodes[parentPath]
		if !exists {
			// This case should be less frequent with sorting, but could happen with malformed archives.
			log.Warn(context.Background(), "Parent node not found", "path", path, "parentPath", parentPath)
			continue
		}

		currentEntry := ports.ArchiveEntry{
			Name:         filepath.Base(path), // Use Base for name
			IsDir:        obj.IsDir(),
			Path:         path,
			Size:         obj.GetSize(),
			ModifiedTime: obj.ModTime(),
			Type:         utils.DetermineFileType(obj.GetName(), obj.IsDir()),
		}

		currentNode := &ports.ArchiveTreeNode{
			Entry:    currentEntry,
			Children: []*ports.ArchiveTreeNode{},
		}

		parentNode.Children = append(parentNode.Children, currentNode)

		if obj.IsDir() {
			nodes[path] = currentNode
		}
	}

	return rootNode, nil
}

func (s *ArchiveService) TreeArchive(ctx context.Context, req ports.ArchiveTreeRequest) (*ports.ArchiveTreeNode, error) {
	cacheKey := fmt.Sprintf("tree:%d:%s:%s:%s:%s", req.StorageID, req.PathInStorage, req.PathInArchive, req.Password, req.Encoding)
	if !req.Refresh {
		s.cacheLock.RLock()
		cached, found := s.treeCache[cacheKey]
		s.cacheLock.RUnlock()
		if found && (cacheTTL == 0 || time.Now().Before(cached.expiration)) {
			return cached.node, nil
		}
	}

	storageObj, err := s.storageService.GetStorageSource(ctx, req.StorageID)
	if err != nil {
		return nil, err
	}
	if !storageObj.Enabled {
		return nil, apperrors.ErrStorageSourceDisabled
	}

	// Try to get archive from our pool first
	_, err = s.getArchiveHandle(ctx, req.StorageID, req.PathInStorage, req.Password, req.Encoding)
	if err != nil {
		return nil, err
	}

	// Get the archive handle to check if we have a cached file index
	poolKey := fmt.Sprintf("%d:%s:%s:%s", req.StorageID, req.PathInStorage, req.Password, req.Encoding)
	s.archivePoolLock.RLock()
	handle := s.archivePool[poolKey]
	s.archivePoolLock.RUnlock()

	// If we have a file index in the handle, use it for super fast tree building
	if handle != nil && len(handle.fileIndex) > 0 {
		// Convert file index to Obj slice
		allObjects := make([]models.Obj, 0, len(handle.fileIndex))
		for _, info := range handle.fileIndex {
			allObjects = append(allObjects, &models.Object{
				Name:     filepath.Base(info.Path),
				Path:     info.Path,
				Size:     info.Size,
				Modified: info.ModifiedTime,
				IsFolder: info.IsDir,
			})
		}

		rootNode, err := buildTreeFromObjectList(req.PathInArchive, allObjects)
		if err != nil {
			return nil, err
		}

		// Cache and return
		s.cacheLock.Lock()
		s.treeCache[cacheKey] = &treeCacheEntry{
			node:       rootNode,
			expiration: time.Now().Add(cacheTTL),
		}
		s.cacheLock.Unlock()

		log.Debug(ctx, "Using cached file index", "path", req.PathInStorage, "count", len(allObjects))

		return rootNode, nil
	}

	// If for some reason we don't have the file index, this should not happen as getArchiveHandle
	// builds the index, but included as a safety fallback
	return nil, fmt.Errorf("failed to get archive metadata for tree building")
}

// GetArchiveFileStream extracts and streams a file from an archive
func (s *ArchiveService) GetArchiveFileStream(ctx context.Context, storageID uint, archivePath string, filePathInArchive string, password string, encoding string, rangeHeader string) (io.ReadCloser, string, int64, int64, string, int, error) {
	log.Debug(ctx, "Starting file extraction", "filePathInArchive", filePathInArchive, "archivePath", archivePath)
	startTime := time.Now()

	// Get archive filesystem from pool (reusing existing connection if possible)
	archiveFS, err := s.getArchiveHandle(ctx, storageID, archivePath, password, encoding)
	if err != nil {
		statusCode := http.StatusInternalServerError

		if errors.Is(err, archiveTool.ErrUnsupportedArchiveFormat) {
			statusCode = http.StatusBadRequest
			return nil, "", 0, 0, "", statusCode, apperrors.ErrUnsupportedArchiveFormat
		}
		if errors.Is(err, archiveTool.ErrPasswordRequired) {
			statusCode = http.StatusBadRequest
			return nil, "", 0, 0, "", statusCode, apperrors.ErrPasswordRequired
		}
		if errors.Is(err, archiveTool.ErrInvalidPassword) {
			statusCode = http.StatusBadRequest
			return nil, "", 0, 0, "", statusCode, apperrors.ErrInvalidPassword
		}

		return nil, "", 0, 0, "", statusCode, filterPasswordError(err)
	}

	log.Debug(ctx, "Opened archive", "time", time.Since(startTime))

	// Open and extract the file
	innerPath := strings.TrimPrefix(filePathInArchive, "/")
	file, err := archiveFS.Open(innerPath)
	if err != nil {
		statusCode := http.StatusInternalServerError

		if errors.Is(err, fs.ErrNotExist) {
			statusCode = http.StatusNotFound
			return nil, "", 0, 0, "", statusCode, apperrors.ErrFileNotFound
		}

		return nil, "", 0, 0, "", statusCode, err
	}

	// Get file info
	info, err := file.(fs.File).Stat()
	if err != nil {
		file.Close()
		return nil, "", 0, 0, "", http.StatusInternalServerError, err
	}

	if info.IsDir() {
		file.Close()
		return nil, "", 0, 0, "", http.StatusBadRequest, apperrors.ErrPathIsDir
	}

	log.Debug(ctx, "Extracted file info", "time", time.Since(startTime))

	// Determine content type based on file extension
	contentType := mime.TypeByExtension(filepath.Ext(filePathInArchive))
	if contentType == "" {
		contentType = "application/octet-stream"
	}

	// Extract filename from path for Content-Disposition header
	filename := filepath.Base(filePathInArchive)

	originalSize := info.Size()
	actualSize := originalSize
	httpStatus := http.StatusOK

	// Handle Range requests
	if rangeHeader != "" {
		ranges, err := http_range.ParseRange(rangeHeader, originalSize)
		if err != nil || len(ranges) == 0 {
			file.Close()
			return nil, "", 0, 0, "", http.StatusRequestedRangeNotSatisfiable, fmt.Errorf("invalid range header: %s", rangeHeader)
		}

		rangeToServe := ranges[0]
		actualSize = rangeToServe.Length
		httpStatus = http.StatusPartialContent

		// Skip to the requested range
		if seeker, ok := file.(io.Seeker); ok {
			_, err := seeker.Seek(rangeToServe.Start, io.SeekStart)
			if err != nil {
				file.Close()
				return nil, "", 0, 0, "", http.StatusInternalServerError, fmt.Errorf("failed to seek in archive file: %w", err)
			}
		} else {
			// If stream doesn't support seeking, discard bytes until the start position
			if rangeToServe.Start > 0 {
				n, err := io.CopyN(io.Discard, file, rangeToServe.Start)
				if err != nil || n < rangeToServe.Start {
					file.Close()
					return nil, "", 0, 0, "", http.StatusInternalServerError, fmt.Errorf("failed to skip bytes in archive file: %w", err)
				}
			}
		}

		// Limit the stream to just the requested length
		file = &readCloserWithLimit{
			file:  file,
			limit: actualSize,
		}
	}

	log.Debug(ctx, "Total setup time", "time", time.Since(startTime))

	// Return the file stream
	return file, contentType, originalSize, actualSize, filename, httpStatus, nil
}

// streamCombinedCloser wraps an io.Reader and multiple io.Closers.
// Read calls are passed to the underlying Reader.
// Close calls Close on all underlying Closers.
type streamCombinedCloser struct {
	reader  io.Reader
	closers []io.Closer
}

func (c *streamCombinedCloser) Read(p []byte) (n int, err error) {
	return c.reader.Read(p)
}

func (c *streamCombinedCloser) Close() error {
	var closeErrors []string
	for _, closer := range c.closers {
		if err := closer.Close(); err != nil {
			closeErrors = append(closeErrors, err.Error())
		}
	}
	if len(closeErrors) > 0 {
		return fmt.Errorf("errors closing streams: %s", strings.Join(closeErrors, "; "))
	}
	return nil
}

// readCloserWithLimit limits the amount of data that can be read from a ReadCloser
type readCloserWithLimit struct {
	file  fs.File // Store the original file to maintain fs.File interface
	limit int64
	read  int64
}

func (r *readCloserWithLimit) Read(p []byte) (int, error) {
	if r.read >= r.limit {
		return 0, io.EOF
	}

	if int64(len(p)) > r.limit-r.read {
		p = p[:r.limit-r.read]
	}

	n, err := r.file.Read(p)
	r.read += int64(n)
	return n, err
}

func (r *readCloserWithLimit) Close() error {
	return r.file.Close()
}

func (r *readCloserWithLimit) Stat() (fs.FileInfo, error) {
	return r.file.Stat()
}

func filterPasswordError(err error) error {
	if err == nil {
		return nil
	}
	errMsg := err.Error()
	if strings.Contains(errMsg, "password") {
		if strings.Contains(errMsg, "wrong") || strings.Contains(errMsg, "incorrect") || strings.Contains(errMsg, "invalid") {
			return apperrors.ErrInvalidPassword
		}
		return apperrors.ErrPasswordRequired
	}

	// Handle encoding errors more gracefully
	if strings.Contains(errMsg, "invalid argument") ||
		strings.Contains(errMsg, "encoding") ||
		strings.Contains(errMsg, "character") ||
		strings.Contains(errMsg, "utf") ||
		strings.Contains(errMsg, "charset") ||
		strings.Contains(errMsg, "readdir") {
		// Log the original error for debugging purposes
		log.Warn(context.Background(), "Encoding error in archive", "error", err)
		return fmt.Errorf("file names in this archive contain special characters that cannot be processed - please try a different archive tool")
	}

	return err
}
