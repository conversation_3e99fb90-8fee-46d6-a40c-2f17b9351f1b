package database

import (
	// Added context
	"database/sql"
	"errors"
	"fmt"
	"log/slog"
	"path/filepath" // 用于构建 file:// URL

	"github.com/Sakura-Byte/kikoeru-go/pkg/config" // 用于获取数据库配置

	"github.com/golang-migrate/migrate/v4"
	"github.com/golang-migrate/migrate/v4/database"         // 用于 NewWithInstance
	"github.com/golang-migrate/migrate/v4/database/mysql"   // MySQL driver for migrate
	"github.com/golang-migrate/migrate/v4/database/sqlite3" // SQLite3 driver for migrate
	_ "github.com/golang-migrate/migrate/v4/source/file"    // File source driver for migrate
)

// RunMigrations 执行数据库迁移
// dbCfg: 应用的数据库配置部分
// appPaths: 应用的路径配置，用于定位 SQLite 文件和迁移文件目录
// migrationsDirName: 存放迁移脚本的目录名 (例如 "migrations")
func RunMigrations(dbCfg config.DatabaseConfig, appPaths config.PathConfig, migrationsDirName string, logger *slog.Logger) error {
	logger.Info("Starting database migration process...")

	var driverName string
	var dsn string
	var dbInstance *sql.DB // migrate 需要一个 *sql.DB 实例

	// 构建迁移文件源 URL (file://path/to/migrations)
	// 假设 migrations 目录在应用根目录的下一级 (与 kikoeru-go 平级或在其内)
	// 我们需要一个绝对路径。
	// 假设 migrationsDirName 是相对于当前工作目录（kikoeru-go）的。
	migrationsPath, err := filepath.Abs(migrationsDirName)
	if err != nil {
		return fmt.Errorf("failed to get absolute path for migrations directory '%s': %w", migrationsDirName, err)
	}
	sourceURL := "file://" + filepath.ToSlash(migrationsPath) // filepath.ToSlash for Windows compatibility in URLs

	logger.Info("Migration source URL", "url", sourceURL)

	switch dbCfg.Driver {
	case "sqlite":
		driverName = "sqlite3"
		dbPath := dbCfg.SQLite.Path
		if !filepath.IsAbs(dbPath) {
			if appPaths.DataDir == "" {
				return fmt.Errorf("RunMigrations: database.sqlite.path is relative but paths.data_dir is not configured")
			}
			dbPath = filepath.Join(appPaths.DataDir, dbPath)
		}
		dsn = dbPath // For sqlite, DSN is just the path

		// migrate 需要一个 *sql.DB 实例，所以我们先打开一个连接
		// 注意：这个连接仅用于 migrate 工具，GORM 会管理自己的连接池
		tempDB, err := sql.Open("sqlite3", dsn)
		if err != nil {
			return fmt.Errorf("failed to open sqlite connection for migration: %w", err)
		}
		defer tempDB.Close()
		dbInstance = tempDB

	case "mysql":
		driverName = "mysql"
		dsn = fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?multiStatements=true", // multiStatements=true is often needed for migrations
			dbCfg.MySQL.User,
			dbCfg.MySQL.Password,
			dbCfg.MySQL.Host,
			dbCfg.MySQL.Port,
			dbCfg.MySQL.DBName,
			// dbCfg.MySQL.Params, // Params might conflict with multiStatements or migrate's DSN parsing
		)
		if dbCfg.MySQL.Params != "" {
			dsn += "&" + dbCfg.MySQL.Params
		}

		tempDB, err := sql.Open("mysql", dsn)
		if err != nil {
			return fmt.Errorf("failed to open mysql connection for migration: %w", err)
		}
		defer tempDB.Close()
		dbInstance = tempDB

	default:
		return fmt.Errorf("unsupported database driver for migration: %s", dbCfg.Driver)
	}

	var dbDriver database.Driver
	if dbCfg.Driver == "sqlite" {
		dbDriver, err = sqlite3.WithInstance(dbInstance, &sqlite3.Config{})
	} else if dbCfg.Driver == "mysql" {
		dbDriver, err = mysql.WithInstance(dbInstance, &mysql.Config{})
	}
	if err != nil {
		return fmt.Errorf("failed to create migrate database driver instance for %s: %w", dbCfg.Driver, err)
	}

	m, err := migrate.NewWithDatabaseInstance(
		sourceURL,
		driverName, // 这个 driverName 必须与 WithInstance 使用的驱动匹配
		dbDriver,
	)
	if err != nil {
		return fmt.Errorf("failed to create migrate instance: %w. DSN: %s, SourceURL: %s", err, dsn, sourceURL)
	}

	logger.Info("Applying database migrations...")
	if err := m.Up(); err != nil {
		if errors.Is(err, migrate.ErrNoChange) {
			logger.Info("No database migrations to apply.")
			return nil
		}
		// 获取源错误和数据库错误
		sourceErr, dbErr := m.Close()
		if sourceErr != nil {
			logger.Error("Error closing migration source", "error", sourceErr)
		}
		if dbErr != nil {
			logger.Error("Error closing migration database connection", "error", dbErr)
		}
		return fmt.Errorf("failed to apply database migrations: %w", err)
	}

	logger.Info("Database migrations applied successfully.")
	return nil
}
