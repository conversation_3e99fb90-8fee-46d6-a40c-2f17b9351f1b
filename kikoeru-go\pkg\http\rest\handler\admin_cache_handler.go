package handler

import (
	"fmt"
	"log/slog"
	"net/http"
	"os"
	"path/filepath"

	"github.com/Sakura-Byte/kikoeru-go/pkg/config"
	"github.com/gin-gonic/gin"
)

type AdminCacheHandler struct {
	appConfig *config.AppConfig
}

func NewAdminCacheHandler(
	appConfig *config.AppConfig,
	logger *slog.Logger,
) *AdminCacheHandler {
	return &AdminCacheHandler{
		appConfig: appConfig,
	}
}

// GetCacheInfo returns information about cache sizes
func (h *AdminCacheHandler) GetCacheInfo(c *gin.Context) {
	coverCacheSize, err := h.calculateDirectorySize(filepath.Join(h.appConfig.Paths.DataDir, h.appConfig.Paths.CoversDir))
	if err != nil {
		// Failed to calculate cover cache size
		coverCacheSize = 0
	}

	fileCacheSize, err := h.calculateDirectorySize(filepath.Join(h.appConfig.Paths.DataDir, h.appConfig.Cache.CacheDir))
	if err != nil {
		// Failed to calculate file cache size
		fileCacheSize = 0
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"cover_cache_size": h.formatFileSize(coverCacheSize),
			"file_cache_size":  h.formatFileSize(fileCacheSize),
		},
	})
}

// ClearCoverCache clears the cover image cache
func (h *AdminCacheHandler) ClearCoverCache(c *gin.Context) {
	coversCacheDir := filepath.Join(h.appConfig.Paths.DataDir, h.appConfig.Paths.CoversDir)
	if err := h.clearDirectory(coversCacheDir); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to clear cover cache"})
		return
	}

	// Recreate the directory
	if err := os.MkdirAll(coversCacheDir, 0750); err != nil {
		// Failed to recreate cover cache directory
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Cover cache cleared successfully",
	})
}

// ClearFileCache clears the file cache
func (h *AdminCacheHandler) ClearFileCache(c *gin.Context) {
	fileCacheDir := filepath.Join(h.appConfig.Paths.DataDir, h.appConfig.Cache.CacheDir)
	if err := h.clearDirectory(fileCacheDir); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to clear file cache"})
		return
	}

	// Recreate the directory
	if err := os.MkdirAll(fileCacheDir, 0750); err != nil {
		// Failed to recreate file cache directory
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "File cache cleared successfully",
	})
}

// ClearAllCache clears all caches
func (h *AdminCacheHandler) ClearAllCache(c *gin.Context) {
	// Clear cover cache
	coversCacheDir := filepath.Join(h.appConfig.Paths.DataDir, h.appConfig.Paths.CoversDir)
	if err := h.clearDirectory(coversCacheDir); err != nil {
		// Failed to clear cover cache
	}

	// Recreate the cover cache directory
	if err := os.MkdirAll(coversCacheDir, 0750); err != nil {
		// Failed to recreate cover cache directory
	}

	// Clear file cache
	fileCacheDir := filepath.Join(h.appConfig.Paths.DataDir, h.appConfig.Cache.CacheDir)
	if err := h.clearDirectory(fileCacheDir); err != nil {
		// Failed to clear file cache
	}

	// Recreate the file cache directory
	if err := os.MkdirAll(fileCacheDir, 0750); err != nil {
		// Failed to recreate file cache directory
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "All caches cleared successfully",
	})
}

// Helper functions
func (h *AdminCacheHandler) calculateDirectorySize(path string) (int64, error) {
	var size int64
	err := filepath.Walk(path, func(_ string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if !info.IsDir() {
			size += info.Size()
		}
		return err
	})
	return size, err
}

func (h *AdminCacheHandler) clearDirectory(path string) error {
	// Check if directory exists
	if _, err := os.Stat(path); os.IsNotExist(err) {
		return nil // Directory doesn't exist, nothing to clear
	}

	// Remove directory and its contents
	return os.RemoveAll(path)
}

func (h *AdminCacheHandler) formatFileSize(size int64) string {
	const (
		B  = 1
		KB = 1024 * B
		MB = 1024 * KB
		GB = 1024 * MB
	)

	switch {
	case size >= GB:
		return fmt.Sprintf("%.2f GB", float64(size)/float64(GB))
	case size >= MB:
		return fmt.Sprintf("%.2f MB", float64(size)/float64(MB))
	case size >= KB:
		return fmt.Sprintf("%.2f KB", float64(size)/float64(KB))
	default:
		return fmt.Sprintf("%d B", size)
	}
}
