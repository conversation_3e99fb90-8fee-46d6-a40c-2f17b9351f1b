package rar

import (
	"bytes"
	"errors"
	"fmt"
	"io"
	"io/fs"
	"os"
	stdpath "path"
	"strings"
	"time"

	"github.com/Sakura-Byte/kikoeru-go/pkg/apperrors"
	"github.com/Sakura-Byte/kikoeru-go/pkg/log"
	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
	"github.com/Sakura-Byte/kikoeru-go/pkg/storage/archive/tool"
	"github.com/Sakura-Byte/kikoeru-go/pkg/storage/stream"
	"github.com/Sakura-Byte/kikoeru-go/pkg/utils"
	"github.com/nwaples/rardecode/v2"
)

// RAR file format constants
const (
	// RAR marker block size
	rarMarkerSize = 7
	// Minimum header size for a RAR file header
	minRarFileHeaderSize = 32

	// HEAD_FLAGS bit flags
	rarFlagEncrypted = 0x0004 // File is encrypted with password
	rarFlagHasExtra  = 0x0008 // File has extra info

	// Search limits for SFX (self-extracting) archives
	maxSfxSearchSize = 1024 * 1024 // Limit search to first 1MB for performance
	sfxBufferSize    = 4096        // Read 4KB at a time when searching
)

// RAR signature bytes for both RAR4 and RAR5
var (
	rarSignature = []byte{0x52, 0x61, 0x72, 0x21, 0x1A, 0x07} // Rar!<SUB><BEL>
)

// RarFileHeaderInfo contains essential information from a RAR file header
type RarFileHeaderInfo struct {
	Encrypted       bool
	HeaderSize      uint16
	CompressedSize  uint32
	HighPackSize    uint32 // For files larger than 2GB
	HasHighPackSize bool
}

// Rar implements the Tool and MultipartTool interfaces for RAR archives.
// It supports both single RAR files and multi-part RAR archives.
//
// Supported multi-part formats:
//   - .part1.rar, .part2.rar, .part3.rar, ... (modern format)
//   - .rar, .r00, .r01, .r02, ... (classic format)
//
// Multi-part archives are automatically detected and handled when the
// appropriate file extensions are encountered.
type Rar struct {
	// If true, errors encountered during reading or writing
	// a file within an archive will be logged and the
	// operation will continue on remaining files.
	ContinueOnError bool

	// Password to open archives.
	Password string
}

func (r *Rar) AcceptedExtensions() []string {
	return []string{".rar"}
}

func (r *Rar) AcceptedMultipartExtensions() map[string]tool.MultipartExtension {
	return map[string]tool.MultipartExtension{
		".rar":       {Pattern: ".r%02d", StartFrom: 0},
		".part1.rar": {Pattern: ".part%d.rar", StartFrom: 2},
	}
}

func (r *Rar) GetMeta(ss *models.StreamWithSeek, args models.ArchiveArgs) (models.ArchiveMeta, error) {
	log.Debug("RAR GetMeta: Using fast path for RAR password detection", "filename", args.Filename)
	startTime := time.Now()

	// Use our custom RAR header parser for checking password protection
	readSeeker := stream.ConvertStreamWithSeekToReaderSeeker(ss)
	isEncrypted, _, err := IsRarPasswordProtected(readSeeker, args.Password)
	if err != nil {
		// Check for common error with self-extracting archives
		if strings.Contains(err.Error(), "RAR signature not found") {
			log.Debug("RAR GetMeta: This appears to be a self-extracting RAR archive (SFX). Looking for RAR signature elsewhere in the file. This may take longer.")
		}

		// Only return password errors, other errors we'll fall back to the regular method
		if errors.Is(err, apperrors.ErrPasswordRequired) ||
			errors.Is(err, apperrors.ErrInvalidPassword) {
			return nil, err
		}
		log.Debug("RAR GetMeta: Fast RAR detection failed, falling back to regular method", "error", err)
	} else {
		log.Debug("RAR GetMeta: Fast RAR detection completed", "elapsed", time.Since(startTime), "isEncrypted", isEncrypted)

		if isEncrypted && args.Password == "" {
			return nil, apperrors.ErrPasswordRequired
		}

		// If it's not encrypted or we have a valid password, we can continue with the regular method
		// but we know it should work now. We don't have a fast way to get the file list,
		// so we still need to use rardecode for that.
	}

	// Reset stream position
	originalPos, err := ss.Seek(0, io.SeekCurrent)
	if err != nil {
		return nil, fmt.Errorf("could not get current stream position: %w", err)
	}
	defer func() {
		_, _ = ss.Seek(originalPos, io.SeekStart)
	}()

	if _, err := ss.Seek(0, io.SeekStart); err != nil {
		return nil, fmt.Errorf("could not reset stream: %w", err)
	}

	var isEncryptedFinal bool
	var objects []models.Obj

	opts := []rardecode.Option{}
	if args.Password != "" {
		opts = append(opts, rardecode.Password(args.Password))
	}

	rr, err := rardecode.NewReader(readSeeker, opts...)
	if err != nil {
		return nil, filterPassword(err)
	}

	for {
		hdr, err := rr.Next()
		if err == io.EOF {
			break
		}
		if err != nil {
			return nil, filterPassword(err)
		}
		if hdr.Encrypted {
			isEncryptedFinal = true
		}
		objects = append(objects, &models.Object{
			Name:     stdpath.Base(hdr.Name),
			Path:     hdr.Name,
			Size:     hdr.UnPackedSize,
			Modified: hdr.ModificationTime,
			IsFolder: hdr.IsDir,
		})
	}
	if isEncryptedFinal && args.Password == "" {
		return nil, apperrors.ErrPasswordRequired
	}

	tree := make([]models.ObjTree, len(objects))
	for i, obj := range objects {
		tree[i] = &models.ObjectTree{Object: *obj.(*models.Object)}
	}

	return &models.ArchiveMetaInfo{
		Comment:   "",
		Encrypted: isEncryptedFinal,
		Tree:      tree,
	}, nil
}

func (r *Rar) List(ss *models.StreamWithSeek, args models.ArchiveInnerArgs) ([]models.Obj, error) {
	fsys, err := r.GetFS(ss, args.ArchiveArgs)
	if err != nil {
		return nil, err
	}
	innerPath := strings.TrimPrefix(args.InnerPath, "/")
	if innerPath == "" {
		innerPath = "."
	}

	// Cast to our rarFS type to access ReadDir
	rarFS, ok := fsys.(*rarFS)
	if !ok {
		return nil, fmt.Errorf("unexpected filesystem type")
	}

	dirEntries, err := rarFS.ReadDir(innerPath)
	if err != nil {
		return nil, filterPassword(err)
	}
	return utils.SliceConvert(dirEntries, func(src fs.DirEntry) (models.Obj, error) {
		info, err := src.Info()
		if err != nil {
			return nil, err
		}
		fullPath := stdpath.Join(args.InnerPath, src.Name())
		if args.InnerPath == "." {
			fullPath = src.Name()
		}
		return &models.Object{
			Name:     info.Name(),
			Path:     fullPath,
			Size:     info.Size(),
			Modified: info.ModTime(),
			IsFolder: info.IsDir(),
		}, nil
	})
}

func (r *Rar) Extract(ss *models.StreamWithSeek, args models.ArchiveInnerArgs) (io.ReadCloser, int64, error) {
	fsys, err := r.GetFS(ss, args.ArchiveArgs)
	if err != nil {
		return nil, 0, err
	}
	file, err := fsys.Open(strings.TrimPrefix(args.InnerPath, "/"))
	if err != nil {
		return nil, 0, filterPassword(err)
	}
	stat, err := file.Stat()
	if err != nil {
		return nil, 0, filterPassword(err)
	}
	return file, stat.Size(), nil
}

func (r *Rar) Decompress(ss *models.StreamWithSeek, outputPath string, args models.ArchiveInnerArgs, up func(float64)) error {
	fsys, err := r.GetFS(ss, args.ArchiveArgs)
	if err != nil {
		return err
	}

	// Cast to our rarFS type to access Stat
	rarFS, ok := fsys.(*rarFS)
	if !ok {
		return fmt.Errorf("unexpected filesystem type")
	}

	isDir := false
	path := strings.TrimPrefix(args.InnerPath, "/")
	if path == "" {
		isDir = true
		path = "."
	} else {
		stat, err := rarFS.Stat(path)
		if err != nil {
			return filterPassword(err)
		}
		if stat.IsDir() {
			isDir = true
			outputPath = stdpath.Join(outputPath, stat.Name())
			err = os.Mkdir(outputPath, 0700)
			if err != nil {
				return filterPassword(err)
			}
		}
	}
	if isDir {
		err = fs.WalkDir(fsys, path, func(p string, d fs.DirEntry, err error) error {
			if err != nil {
				return err
			}
			relPath := strings.TrimPrefix(p, path+"/")
			dstPath := stdpath.Join(outputPath, relPath)
			if d.IsDir() {
				err = os.MkdirAll(dstPath, 0700)
			} else {
				dir := stdpath.Dir(dstPath)
				err = decompress(fsys, p, dir, func(_ float64) {})
			}
			return err
		})
	} else {
		err = decompress(fsys, path, outputPath, up)
	}
	return filterPassword(err)
}

func (r *Rar) GetFS(ss *models.StreamWithSeek, args models.ArchiveArgs) (fs.FS, error) {
	readSeeker := stream.ConvertStreamWithSeekToReaderSeeker(ss)

	opts := []rardecode.Option{}
	if args.Password != "" {
		opts = append(opts, rardecode.Password(args.Password))
	}

	// Reset to beginning
	if _, err := readSeeker.Seek(0, io.SeekStart); err != nil {
		return nil, fmt.Errorf("failed to seek to start: %w", err)
	}

	rr, err := rardecode.NewReader(readSeeker, opts...)
	if err != nil {
		return nil, filterPassword(err)
	}

	return &rarFS{
		reader:     rr,
		readSeeker: readSeeker,
		password:   args.Password,
	}, nil
}

// VolumeFile represents a single volume file in a multi-part RAR archive
type VolumeFile struct {
	stream *models.StreamWithSeek
	name   string
}

func (v *VolumeFile) Name() string {
	return v.name
}

func (v *VolumeFile) Size() int64 {
	return v.stream.GetSize()
}

func (v *VolumeFile) Mode() fs.FileMode {
	return 0644
}

func (v *VolumeFile) ModTime() time.Time {
	return time.Now() // Use current time as we don't have access to the original mod time
}

func (v *VolumeFile) IsDir() bool {
	return false
}

func (v *VolumeFile) Sys() any {
	return nil
}

func (v *VolumeFile) Stat() (fs.FileInfo, error) {
	return v, nil
}

func (v *VolumeFile) Read(p []byte) (n int, err error) {
	return v.stream.Read(p)
}

func (v *VolumeFile) ReadAt(p []byte, off int64) (n int, err error) {
	// Save current position
	currentPos, err := v.stream.Seek(0, io.SeekCurrent)
	if err != nil {
		return 0, err
	}

	// Seek to offset
	_, err = v.stream.Seek(off, io.SeekStart)
	if err != nil {
		return 0, err
	}

	// Read data
	n, err = v.stream.Read(p)

	// Restore position
	_, seekErr := v.stream.Seek(currentPos, io.SeekStart)
	if seekErr != nil && err == nil {
		err = seekErr
	}

	return n, err
}

func (v *VolumeFile) Seek(offset int64, whence int) (int64, error) {
	return v.stream.Seek(offset, whence)
}

func (v *VolumeFile) Close() error {
	return v.stream.Close()
}

// VolumeFS implements fs.FS for multi-part RAR archives
type VolumeFS struct {
	parts map[string]*VolumeFile
}

func (v *VolumeFS) Open(name string) (fs.File, error) {
	file, ok := v.parts[name]
	if !ok {
		return nil, fs.ErrNotExist
	}
	return file, nil
}

// rarFS implements fs.FS for RAR archives
type rarFS struct {
	reader     *rardecode.Reader
	readSeeker io.ReadSeeker
	password   string
	fileIndex  map[string]*rardecode.FileHeader
	indexBuilt bool
}

func (r *rarFS) buildIndex() error {
	if r.indexBuilt {
		return nil
	}

	// Reset to beginning and create new reader
	if _, err := r.readSeeker.Seek(0, io.SeekStart); err != nil {
		return err
	}

	opts := []rardecode.Option{}
	if r.password != "" {
		opts = append(opts, rardecode.Password(r.password))
	}

	reader, err := rardecode.NewReader(r.readSeeker, opts...)
	if err != nil {
		return err
	}

	r.fileIndex = make(map[string]*rardecode.FileHeader)

	for {
		hdr, err := reader.Next()
		if err == io.EOF {
			break
		}
		if err != nil {
			return err
		}
		r.fileIndex[hdr.Name] = hdr
	}

	r.indexBuilt = true
	return nil
}

func (r *rarFS) Open(name string) (fs.File, error) {
	if err := r.buildIndex(); err != nil {
		return nil, err
	}

	name = strings.TrimPrefix(name, "/")
	if name == "." {
		return &rarDir{fs: r, path: ""}, nil
	}

	hdr, exists := r.fileIndex[name]
	if !exists {
		return nil, fs.ErrNotExist
	}

	if hdr.IsDir {
		return &rarDir{fs: r, path: name}, nil
	}

	// Reset and create new reader to extract this specific file
	if _, err := r.readSeeker.Seek(0, io.SeekStart); err != nil {
		return nil, err
	}

	opts := []rardecode.Option{}
	if r.password != "" {
		opts = append(opts, rardecode.Password(r.password))
	}

	reader, err := rardecode.NewReader(r.readSeeker, opts...)
	if err != nil {
		return nil, err
	}

	// Find the file in the archive
	for {
		fileHdr, err := reader.Next()
		if err == io.EOF {
			return nil, fs.ErrNotExist
		}
		if err != nil {
			return nil, err
		}
		if fileHdr.Name == name {
			return &rarFile{
				header: fileHdr,
				reader: reader,
			}, nil
		}
	}
}

func (r *rarFS) ReadDir(name string) ([]fs.DirEntry, error) {
	if err := r.buildIndex(); err != nil {
		return nil, err
	}

	name = strings.TrimPrefix(name, "/")
	if name == "." {
		name = ""
	}

	var entries []fs.DirEntry
	seen := make(map[string]bool)

	for path, hdr := range r.fileIndex {
		if name == "" {
			// Root directory - find top-level entries
			parts := strings.Split(path, "/")
			if len(parts) > 0 {
				topLevel := parts[0]
				if !seen[topLevel] {
					seen[topLevel] = true
					if len(parts) == 1 {
						// It's a file in root
						entries = append(entries, &rarDirEntry{header: hdr})
					} else {
						// It's a directory
						entries = append(entries, &rarDirEntry{
							header: &rardecode.FileHeader{
								Name:  topLevel,
								IsDir: true,
							},
						})
					}
				}
			}
		} else {
			// Subdirectory - find entries that are direct children
			if strings.HasPrefix(path, name+"/") {
				relativePath := strings.TrimPrefix(path, name+"/")
				parts := strings.Split(relativePath, "/")
				if len(parts) > 0 {
					childName := parts[0]
					if !seen[childName] {
						seen[childName] = true
						if len(parts) == 1 {
							// Direct child file
							entries = append(entries, &rarDirEntry{header: hdr})
						} else {
							// Direct child directory
							entries = append(entries, &rarDirEntry{
								header: &rardecode.FileHeader{
									Name:  stdpath.Join(name, childName),
									IsDir: true,
								},
							})
						}
					}
				}
			}
		}
	}

	return entries, nil
}

func (r *rarFS) Stat(name string) (fs.FileInfo, error) {
	if err := r.buildIndex(); err != nil {
		return nil, err
	}

	name = strings.TrimPrefix(name, "/")
	if name == "." {
		return &rarFileInfo{
			header: &rardecode.FileHeader{
				Name:  "",
				IsDir: true,
			},
		}, nil
	}

	hdr, exists := r.fileIndex[name]
	if !exists {
		return nil, fs.ErrNotExist
	}

	return &rarFileInfo{header: hdr}, nil
}

// rarFile implements fs.File for RAR archive files
type rarFile struct {
	header *rardecode.FileHeader
	reader *rardecode.Reader
}

func (f *rarFile) Read(p []byte) (n int, err error) {
	return f.reader.Read(p)
}

func (f *rarFile) Close() error {
	return nil // rardecode.Reader doesn't need explicit closing
}

func (f *rarFile) Stat() (fs.FileInfo, error) {
	return &rarFileInfo{header: f.header}, nil
}

// rarDir implements fs.File for RAR archive directories
type rarDir struct {
	fs   *rarFS
	path string
}

func (d *rarDir) Read(p []byte) (n int, err error) {
	return 0, fmt.Errorf("cannot read from directory")
}

func (d *rarDir) Close() error {
	return nil
}

func (d *rarDir) Stat() (fs.FileInfo, error) {
	return &rarFileInfo{
		header: &rardecode.FileHeader{
			Name:  d.path,
			IsDir: true,
		},
	}, nil
}

func (d *rarDir) ReadDir(n int) ([]fs.DirEntry, error) {
	entries, err := d.fs.ReadDir(d.path)
	if err != nil {
		return nil, err
	}
	if n <= 0 {
		return entries, nil
	}
	if n > len(entries) {
		n = len(entries)
	}
	return entries[:n], nil
}

// rarDirEntry implements fs.DirEntry for RAR archive entries
type rarDirEntry struct {
	header *rardecode.FileHeader
}

func (e *rarDirEntry) Name() string {
	return stdpath.Base(e.header.Name)
}

func (e *rarDirEntry) IsDir() bool {
	return e.header.IsDir
}

func (e *rarDirEntry) Type() fs.FileMode {
	if e.header.IsDir {
		return fs.ModeDir
	}
	return 0
}

func (e *rarDirEntry) Info() (fs.FileInfo, error) {
	return &rarFileInfo{header: e.header}, nil
}

// rarFileInfo implements fs.FileInfo for RAR archive entries
type rarFileInfo struct {
	header *rardecode.FileHeader
}

func (i *rarFileInfo) Name() string {
	return stdpath.Base(i.header.Name)
}

func (i *rarFileInfo) Size() int64 {
	return i.header.UnPackedSize
}

func (i *rarFileInfo) Mode() os.FileMode {
	return i.header.Mode()
}

func (i *rarFileInfo) ModTime() time.Time {
	return i.header.ModificationTime
}

func (i *rarFileInfo) IsDir() bool {
	return i.header.IsDir
}

func (i *rarFileInfo) Sys() any {
	return nil
}

// Helper functions

// IsRarPasswordProtected quickly checks if a RAR file requires a password
// by attempting to open it with rardecode. This is more reliable than
// manual header parsing, especially for SFX archives.
//
// Returns:
// - isEncrypted: true if the archive contains encrypted files
// - workingPassword: the provided password if it works, empty string otherwise
// - error: any error encountered during the check
func IsRarPasswordProtected(r io.ReadSeeker, password string) (isEncrypted bool, workingPassword string, err error) {
	// Save current position to restore it later, making this a non-destructive check.
	startPos, err := r.Seek(0, io.SeekCurrent)
	if err != nil {
		return false, "", fmt.Errorf("failed to get current stream position: %w", err)
	}
	defer r.Seek(startPos, io.SeekStart)

	// First, try to find the RAR signature - accounting for both normal RAR and SFX (self-extracting) archives
	offset, _, err := findRarSignature(r)
	if err != nil {
		// Pass signature not found error up, as it indicates a non-RAR file or SFX we can't parse.
		if strings.Contains(err.Error(), "RAR signature not found") {
			return false, "", err
		}
		return false, "", err
	}

	// Seek to the position of the signature
	if _, err := r.Seek(offset, io.SeekStart); err != nil {
		return false, "", fmt.Errorf("failed to seek to RAR header: %w", err)
	}

	// Use rardecode to perform the check.
	// It will fail early if headers are encrypted and the password is wrong or missing.
	opts := []rardecode.Option{}
	if password != "" {
		opts = append(opts, rardecode.Password(password))
	}

	rd, err := rardecode.NewReader(r, opts...)
	if err != nil {
		errMsg := strings.ToLower(err.Error())
		// Check for password-related errors from rardecode
		if strings.Contains(errMsg, "password") || strings.Contains(errMsg, "encrypted") {
			// The archive is encrypted, but the provided password (if any) is incorrect.
			if password == "" {
				return true, "", apperrors.ErrPasswordRequired
			}
			return true, "", apperrors.ErrInvalidPassword
		}
		// A different error occurred during initialization.
		return false, "", fmt.Errorf("rardecode initialization failed: %w", err)
	}

	// If NewReader succeeds, headers are not encrypted or the password was correct for them.
	// Now, we must iterate through the file entries to check if any individual file is encrypted.
	// This is because an archive can have unencrypted headers but encrypted file data.
	hasEncryptedFiles := false
	for {
		hdr, err := rd.Next()
		if err == io.EOF {
			// Reached the end of the archive.
			break
		}
		if err != nil {
			// An error during iteration can also indicate a password error,
			// e.g., for a file with an encrypted header that NewReader didn't check.
			errMsg := strings.ToLower(err.Error())
			if strings.Contains(errMsg, "password") || strings.Contains(errMsg, "checksum") || strings.Contains(errMsg, "decrypt") {
				if password == "" {
					return true, "", apperrors.ErrPasswordRequired
				}
				return true, "", apperrors.ErrInvalidPassword
			}
			// Another type of error while reading the archive directory.
			return false, "", fmt.Errorf("error iterating rar entries: %w", err)
		}

		if hdr.Encrypted {
			hasEncryptedFiles = true
			// We can break early, we just need to know if at least one file is encrypted.
			break
		}
	}

	if hasEncryptedFiles {
		if password == "" {
			// The file is encrypted, but we weren't given a password to test.
			return true, "", apperrors.ErrPasswordRequired
		}
		// An encrypted file was found, and we had a password. Since we were able
		// to read the header without an error, the password must be correct.
		return true, password, nil
	}

	// If we've looped through all entries and found none marked as encrypted,
	// and had no password errors, the archive is not password-protected.
	return false, "", nil
}

// findRarSignature looks for the RAR signature in a file, supporting both normal RAR files
// and self-extracting (SFX) RAR executables. Returns the offset where the signature was found,
// a boolean indicating if it's RAR5 format, and any error encountered.
func findRarSignature(r io.ReadSeeker) (int64, bool, error) {
	// First check if it's a standard RAR file with signature at the beginning
	marker := make([]byte, rarMarkerSize)
	if _, err := io.ReadFull(r, marker); err != nil {
		return 0, false, fmt.Errorf("failed to read initial bytes: %w", err)
	}

	// Check if it starts with the RAR signature
	if bytes.Equal(marker[:6], rarSignature) {
		// It's a standard RAR file
		isRar5 := marker[6] == 0x01
		return 0, isRar5, nil
	}

	// If not at the beginning, it might be a self-extracting archive (SFX)
	// Reset to beginning
	if _, err := r.Seek(0, io.SeekStart); err != nil {
		return 0, false, fmt.Errorf("failed to seek to beginning for SFX search: %w", err)
	}

	// Search for the RAR signature in the first part of the file
	// SFX archives are EXE files with RAR data appended, usually within the first 1MB
	// but sometimes can be farther into the file
	buffer := make([]byte, sfxBufferSize)
	totalBytesRead := int64(0)
	foundOffset := int64(-1)
	isRar5 := false

	// Read with overlap to ensure we don't miss signatures split across buffer boundaries
	overlapSize := 16 // More than enough for the RAR signature
	lastBufferBytes := 0

	// For self-extracting archives, check for common offsets first
	// These are typical locations where SFX archives store the RAR data
	commonOffsets := []int64{0x4000, 0x8000, 0x10000, 0x20000, 0x40000, 0x80000}

	for _, offset := range commonOffsets {
		if offset >= maxSfxSearchSize {
			break
		}

		// Seek to the common offset and check
		if _, err := r.Seek(offset, io.SeekStart); err != nil {
			// If we can't seek this far, the file is too small
			break
		}

		// Read enough bytes to check for the signature
		if _, err := io.ReadFull(r, marker); err != nil {
			// If we can't read enough bytes, skip this offset
			continue
		}

		// Check if this offset contains the RAR signature
		if bytes.Equal(marker[:6], rarSignature) {
			// Found the signature at a common offset!
			isRar5 = marker[6] == 0x01
			log.Debug("Found RAR signature at common offset (SFX executable detected)", "offset", fmt.Sprintf("0x%X", offset))
			return offset, isRar5, nil
		}
	}

	// Reset to beginning for the full search
	if _, err := r.Seek(0, io.SeekStart); err != nil {
		return 0, false, fmt.Errorf("failed to seek to beginning for SFX search: %w", err)
	}

	// If common offsets didn't work, do a thorough scan of the file
	log.Debug("Scanning for RAR signature in possible SFX archive (this may take a moment)...")

	for totalBytesRead < maxSfxSearchSize {
		// Position to read from, accounting for overlap
		readPos := totalBytesRead
		if lastBufferBytes > 0 {
			readPos -= int64(overlapSize)
		}

		// Seek to the correct position
		if _, err := r.Seek(readPos, io.SeekStart); err != nil {
			return 0, false, fmt.Errorf("error seeking during SFX search: %w", err)
		}

		// Read a buffer full of data
		bytesRead, err := r.Read(buffer)
		if err != nil && err != io.EOF {
			return 0, false, fmt.Errorf("error reading file during SFX search: %w", err)
		}

		if bytesRead == 0 {
			// End of file reached
			break
		}

		// Look for RAR signature in this chunk
		for i := 0; i <= bytesRead-rarMarkerSize; i++ {
			if bytes.Equal(buffer[i:i+6], rarSignature) {
				// Found it! Calculate the absolute offset
				foundOffset = readPos + int64(i)
				isRar5 = buffer[i+6] == 0x01
				break
			}
		}

		if foundOffset >= 0 {
			break
		}

		// Update for next iteration
		lastBufferBytes = bytesRead
		totalBytesRead += int64(bytesRead - overlapSize)

		if bytesRead < sfxBufferSize {
			// End of file reached
			break
		}

		// Give some progress indication for large files
		if totalBytesRead > 0 && totalBytesRead%int64(sfxBufferSize*32) == 0 {
			log.Debug("Still searching for RAR signature...", "scanned_kb", totalBytesRead/1024)
		}
	}

	if foundOffset < 0 {
		return 0, false, fmt.Errorf("RAR signature not found in the first %d bytes. This may not be a valid RAR archive or SFX.", maxSfxSearchSize)
	}

	log.Debug("Found RAR signature (SFX executable detected)", "offset", foundOffset)
	return foundOffset, isRar5, nil
}

// filterPassword filters errors related to archive passwords
func filterPassword(err error) error {
	// Check if the error is related to password issues
	if err == nil {
		return nil
	}

	errMsg := err.Error()
	log.Debug("filterPassword: analyzing error", "error", err)

	if strings.Contains(errMsg, "password") ||
		strings.Contains(errMsg, "encrypted") ||
		strings.Contains(errMsg, "decrypt") ||
		strings.Contains(errMsg, "authentication failed") {

		log.Debug("filterPassword: detected password-related error")

		// Different archive tools use different error messages for wrong passwords
		if strings.Contains(errMsg, "wrong") ||
			strings.Contains(errMsg, "incorrect") ||
			strings.Contains(errMsg, "invalid") ||
			strings.Contains(errMsg, "bad") ||
			strings.Contains(errMsg, "authentication failed") {
			log.Debug("filterPassword: detected invalid password error")
			return apperrors.ErrInvalidPassword
		}

		log.Debug("filterPassword: detected password required error")
		return apperrors.ErrPasswordRequired
	}
	return err
}

// decompress extracts a file from an archive to a directory
func decompress(fsys fs.FS, path string, outputPath string, up func(float64)) error {
	file, err := fsys.Open(path)
	if err != nil {
		return err
	}
	defer file.Close()

	// Get file info to determine size
	info, err := file.Stat()
	if err != nil {
		return err
	}

	// Create output file
	fileName := stdpath.Base(path)
	outputFile, err := os.Create(stdpath.Join(outputPath, fileName))
	if err != nil {
		return err
	}
	defer outputFile.Close()

	// Copy with progress updates
	return copyWithProgress(outputFile, file, info.Size(), up)
}

// copyWithProgress copies data from src to dst with progress updates
func copyWithProgress(dst io.Writer, src io.Reader, totalSize int64, up func(float64)) error {
	buf := make([]byte, 32*1024) // 32KB buffer
	var written int64

	for {
		nr, er := src.Read(buf)
		if nr > 0 {
			nw, ew := dst.Write(buf[:nr])
			if nw > 0 {
				written += int64(nw)
				if totalSize > 0 {
					up(float64(written) / float64(totalSize) * 100)
				}
			}
			if ew != nil {
				return ew
			}
			if nr != nw {
				return io.ErrShortWrite
			}
		}
		if er != nil {
			if er == io.EOF {
				return nil
			}
			return er
		}
	}
}

// makeVolumeOpts creates rardecode options for multi-part archives
func makeVolumeOpts(streams []*models.StreamWithSeek, password string) (string, []rardecode.Option, error) {
	if len(streams) == 1 {
		// Single volume
		fileName := "file.rar"
		parts := map[string]*VolumeFile{
			fileName: {stream: streams[0], name: fileName},
		}
		fsys := &VolumeFS{parts: parts}

		opts := []rardecode.Option{rardecode.FileSystem(fsys)}
		if password != "" {
			opts = append(opts, rardecode.Password(password))
		}
		return fileName, opts, nil
	} else {
		// Multi-part archive
		parts := make(map[string]*VolumeFile, len(streams))
		for i, stream := range streams {
			fileName := fmt.Sprintf("file.part%d.rar", i+1)
			parts[fileName] = &VolumeFile{stream: stream, name: fileName}
		}
		fsys := &VolumeFS{parts: parts}

		opts := []rardecode.Option{rardecode.FileSystem(fsys)}
		if password != "" {
			opts = append(opts, rardecode.Password(password))
		}
		return "file.part1.rar", opts, nil
	}
}

// Multi-part archive methods

func (r *Rar) GetMetaMultipart(streams []*models.StreamWithSeek, args models.ArchiveArgs) (models.ArchiveMeta, error) {
	fileName, opts, err := makeVolumeOpts(streams, args.Password)
	if err != nil {
		return nil, err
	}

	files, err := rardecode.List(fileName, opts...)
	if err != nil {
		return nil, filterPassword(err)
	}

	var objects []models.Obj
	var isEncrypted bool

	for _, file := range files {
		if file.Encrypted {
			isEncrypted = true
		}
		objects = append(objects, &models.Object{
			Name:     stdpath.Base(file.Name),
			Path:     file.Name,
			Size:     file.UnPackedSize,
			Modified: file.ModificationTime,
			IsFolder: file.IsDir,
		})
	}

	if isEncrypted && args.Password == "" {
		return nil, apperrors.ErrPasswordRequired
	}

	tree := make([]models.ObjTree, len(objects))
	for i, obj := range objects {
		tree[i] = &models.ObjectTree{Object: *obj.(*models.Object)}
	}

	return &models.ArchiveMetaInfo{
		Comment:   "",
		Encrypted: isEncrypted,
		Tree:      tree,
	}, nil
}

func (r *Rar) ListMultipart(streams []*models.StreamWithSeek, args models.ArchiveInnerArgs) ([]models.Obj, error) {
	fsys, err := r.GetFSMultipart(streams, args.ArchiveArgs)
	if err != nil {
		return nil, err
	}

	// Cast to our rarFS type to access ReadDir
	rarFS, ok := fsys.(*rarFS)
	if !ok {
		return nil, fmt.Errorf("unexpected filesystem type")
	}

	innerPath := strings.TrimPrefix(args.InnerPath, "/")
	if innerPath == "" {
		innerPath = "."
	}

	dirEntries, err := rarFS.ReadDir(innerPath)
	if err != nil {
		return nil, filterPassword(err)
	}

	return utils.SliceConvert(dirEntries, func(src fs.DirEntry) (models.Obj, error) {
		info, err := src.Info()
		if err != nil {
			return nil, err
		}
		fullPath := stdpath.Join(args.InnerPath, src.Name())
		if args.InnerPath == "." {
			fullPath = src.Name()
		}
		return &models.Object{
			Name:     info.Name(),
			Path:     fullPath,
			Size:     info.Size(),
			Modified: info.ModTime(),
			IsFolder: info.IsDir(),
		}, nil
	})
}

func (r *Rar) ExtractMultipart(streams []*models.StreamWithSeek, args models.ArchiveInnerArgs) (io.ReadCloser, int64, error) {
	fileName, opts, err := makeVolumeOpts(streams, args.ArchiveArgs.Password)
	if err != nil {
		return nil, 0, err
	}

	rc, err := rardecode.OpenReader(fileName, opts...)
	if err != nil {
		return nil, 0, filterPassword(err)
	}

	innerPath := strings.TrimPrefix(args.InnerPath, "/")
	for {
		header, err := rc.Next()
		if err == io.EOF {
			rc.Close()
			return nil, 0, fs.ErrNotExist
		}
		if err != nil {
			rc.Close()
			return nil, 0, filterPassword(err)
		}
		if header.Name == innerPath {
			if header.IsDir {
				rc.Close()
				return nil, 0, fmt.Errorf("cannot extract directory")
			}
			return &rarReadCloser{Reader: &rc.Reader, closer: rc}, header.UnPackedSize, nil
		}
	}
}

func (r *Rar) DecompressMultipart(streams []*models.StreamWithSeek, outputPath string, args models.ArchiveInnerArgs, up func(float64)) error {
	fsys, err := r.GetFSMultipart(streams, args.ArchiveArgs)
	if err != nil {
		return err
	}

	// Cast to our rarFS type to access Stat
	rarFS, ok := fsys.(*rarFS)
	if !ok {
		return fmt.Errorf("unexpected filesystem type")
	}

	isDir := false
	path := strings.TrimPrefix(args.InnerPath, "/")
	if path == "" {
		isDir = true
		path = "."
	} else {
		stat, err := rarFS.Stat(path)
		if err != nil {
			return filterPassword(err)
		}
		if stat.IsDir() {
			isDir = true
			outputPath = stdpath.Join(outputPath, stat.Name())
			err = os.Mkdir(outputPath, 0700)
			if err != nil {
				return filterPassword(err)
			}
		}
	}
	if isDir {
		err = fs.WalkDir(fsys, path, func(p string, d fs.DirEntry, err error) error {
			if err != nil {
				return err
			}
			relPath := strings.TrimPrefix(p, path+"/")
			dstPath := stdpath.Join(outputPath, relPath)
			if d.IsDir() {
				err = os.MkdirAll(dstPath, 0700)
			} else {
				dir := stdpath.Dir(dstPath)
				err = decompress(fsys, p, dir, func(_ float64) {})
			}
			return err
		})
	} else {
		err = decompress(fsys, path, outputPath, up)
	}
	return filterPassword(err)
}

func (r *Rar) GetFSMultipart(streams []*models.StreamWithSeek, args models.ArchiveArgs) (fs.FS, error) {
	fileName, opts, err := makeVolumeOpts(streams, args.Password)
	if err != nil {
		return nil, err
	}

	rc, err := rardecode.OpenReader(fileName, opts...)
	if err != nil {
		return nil, filterPassword(err)
	}

	return &rarFS{
		reader:     &rc.Reader,
		readSeeker: nil, // Not used for multi-part
		password:   args.Password,
	}, nil
}

// rarReadCloser wraps a rardecode.Reader with a closer
type rarReadCloser struct {
	*rardecode.Reader
	closer io.Closer
}

func (r *rarReadCloser) Close() error {
	return r.closer.Close()
}

var _ tool.Tool = (*Rar)(nil)
var _ tool.MultipartTool = (*Rar)(nil)

func init() {
	tool.RegisterTool(&Rar{})
}
