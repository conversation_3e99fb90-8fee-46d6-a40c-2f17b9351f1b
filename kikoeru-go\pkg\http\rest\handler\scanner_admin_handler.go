package handler

import (
	"context" // Added for context.Background()
	"fmt"
	"log/slog"
	"net/http"

	"github.com/Sakura-Byte/kikoeru-go/pkg/http/rest/common"
	"github.com/Sakura-Byte/kikoeru-go/pkg/scanner" // Assuming ScannerService is in this package
	"github.com/gin-gonic/gin"
)

type ScannerAdminHandler struct {
	scannerService *scanner.ScannerService // Direct reference to ScannerService
	logger         *slog.Logger
}

func NewScannerAdminHandler(scannerService *scanner.ScannerService, logger *slog.Logger) *ScannerAdminHandler {
	return &ScannerAdminHandler{
		scannerService: scannerService,
		logger:         logger.With("handler", "ScannerAdminHandler"),
	}
}

// TriggerLibraryScan handles POST /api/v1/admin/scanner/scan/library/:libraryName
func (h *ScannerAdminHandler) TriggerLibraryScan(c *gin.Context) {
	storageIdentifier := c.Param("libraryName") // Path param is still :libraryName
	if storageIdentifier == "" {
		common.SendErrorResponse(c, http.StatusBadRequest, "Storage identifier (ID or remark) cannot be empty")
		return
	}

	h.logger.InfoContext(c.Request.Context(), "Admin request to scan library", "storage_identifier", storageIdentifier)

	// Run scan in a new goroutine so the API call returns immediately.
	// The client can monitor progress via WebSocket or GetStatus API.
	go func() {
		// Create a new background context for the scan as the HTTP request context will be cancelled.
		scanCtx := context.Background() // Or context.TODO()
		// When ScanLibrary is called directly by admin handler, it's not tied to a specific BackgroundTask's progress.
		// Thus, taskIDForProgress is empty and updater is nil.
		if err := h.scannerService.ScanLibrary(scanCtx, storageIdentifier, "", nil); err != nil {
			h.logger.ErrorContext(scanCtx, "Error occurred during admin-triggered library scan", "storage_identifier", storageIdentifier, "error", err)
			// Logging is sufficient here; status is updated by ScannerService itself.
		}
	}()

	common.SendSuccessResponse(c, http.StatusAccepted, gin.H{"message": fmt.Sprintf("Scan initiated for storage: %s. Monitor status via WebSocket or GetStatus API.", storageIdentifier)})
}

// GetScannerStatus handles GET /api/v1/admin/scanner/status
// This provides an HTTP endpoint to get the overall scanner status.
func (h *ScannerAdminHandler) GetScannerStatus(c *gin.Context) {
	status := h.scannerService.GetStatus()
	common.SendSuccessResponse(c, http.StatusOK, status)
}
