package handler

import (
	"encoding/base64"
	"errors"
	"fmt"
	"io"
	"log/slog"
	"net/http"
	"strconv"

	"github.com/Sakura-Byte/kikoeru-go/pkg/apperrors" // Added for apperrors
	"github.com/Sakura-Byte/kikoeru-go/pkg/http/rest/common"
	"github.com/Sakura-Byte/kikoeru-go/pkg/ports" // Changed from service to ports
	"github.com/gin-gonic/gin"
)

type FileSystemHandler struct {
	service ports.FileSystemService
	logger  *slog.Logger
}

func NewFileSystemHandler(fsService ports.FileSystemService, logger *slog.Logger) *FileSystemHandler {
	return &FileSystemHandler{
		service: fsService,
		logger:  logger.With("handler", "FileSystemHandler"),
	}
}

// ListFilesOrDirectories handles POST /api/v1/fs/list
func (h *FileSystemHandler) ListFilesOrDirectories(c *gin.Context) {
	var req ports.ListRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WarnContext(c.Request.Context(), "Failed to bind JSON for ListFilesOrDirectories", "error", err)
		common.SendErrorResponse(c, http.StatusBadRequest, fmt.Sprintf("Invalid request payload: %v", err))
		return
	}

	entries, err := h.service.List(c.Request.Context(), req)
	if err != nil {
		h.logger.ErrorContext(c.Request.Context(), "Failed to list files/directories", "request", req, "error", err)
		if errors.Is(err, apperrors.ErrStorageNotFound) || errors.Is(err, apperrors.ErrPathNotFound) { // Use apperrors
			common.SendErrorResponse(c, http.StatusNotFound, err.Error())
		} else if errors.Is(err, apperrors.ErrPathIsNotDir) { // Use apperrors
			common.SendErrorResponse(c, http.StatusBadRequest, err.Error())
		} else {
			common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to list files or directories")
		}
		return
	}
	if entries == nil {
		entries = []ports.FileSystemEntry{}
	}
	common.SendSuccessResponse(c, http.StatusOK, entries)
}

// GetFileSystemTree handles POST /api/v1/fs/tree
func (h *FileSystemHandler) GetFileSystemTree(c *gin.Context) {
	var req ports.TreeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WarnContext(c.Request.Context(), "Failed to bind JSON for GetFileSystemTree", "error", err)
		common.SendErrorResponse(c, http.StatusBadRequest, fmt.Sprintf("Invalid request payload: %v", err))
		return
	}

	tree, err := h.service.Tree(c.Request.Context(), req)
	if err != nil {
		h.logger.ErrorContext(c.Request.Context(), "Failed to get file system tree", "request", req, "error", err)
		if errors.Is(err, apperrors.ErrStorageNotFound) || errors.Is(err, apperrors.ErrPathNotFound) {
			common.SendErrorResponse(c, http.StatusNotFound, err.Error())
		} else if errors.Is(err, apperrors.ErrPathIsNotDir) {
			common.SendErrorResponse(c, http.StatusBadRequest, err.Error())
		} else {
			common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to get file system tree")
		}
		return
	}

	if tree == nil {
		// Return empty object if tree is nil
		common.SendSuccessResponse(c, http.StatusOK, map[string]interface{}{})
		return
	}

	common.SendSuccessResponse(c, http.StatusOK, tree)
}

// GetFileLink handles GET /api/v1/fs/link
func (h *FileSystemHandler) GetFileLink(c *gin.Context) {
	storageIDStr := c.Query("storage_id")
	path := c.Query("path")

	if storageIDStr == "" {
		common.SendErrorResponse(c, http.StatusBadRequest, "storage_id query parameter is required")
		return
	}
	if path == "" {
		common.SendErrorResponse(c, http.StatusBadRequest, "path query parameter is required")
		return
	}

	storageID, err := strconv.ParseUint(storageIDStr, 10, 32)
	if err != nil {
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid storage_id format")
		return
	}

	linkResp, err := h.service.GetLink(c.Request.Context(), uint(storageID), path)
	if err != nil {
		if errors.Is(err, apperrors.ErrStorageNotFound) || errors.Is(err, apperrors.ErrFileNotFound) { // Use apperrors
			common.SendErrorResponse(c, http.StatusNotFound, err.Error())
		} else if errors.Is(err, apperrors.ErrPathIsDir) { // Use apperrors
			common.SendErrorResponse(c, http.StatusBadRequest, err.Error())
		} else {
			h.logger.ErrorContext(c.Request.Context(), "Failed to get file link", "storage_id", storageID, "path", path, "error", err)
			common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to get file link")
		}
		return
	}
	common.SendSuccessResponse(c, http.StatusOK, linkResp)
}

// ProxyFileStream handles GET /api/v1/proxy/{storage_id}/{encoded_path}
func (h *FileSystemHandler) ProxyFileStream(c *gin.Context) {
	storageIDStr := c.Param("storage_id")
	encodedPath := c.Param("encoded_path")

	storageID, err := strconv.ParseUint(storageIDStr, 10, 32)
	if err != nil {
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid storage_id in path")
		return
	}

	decodedPathBytes, err := base64.URLEncoding.DecodeString(encodedPath)
	if err != nil {
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid encoded_path: not valid base64 URL encoding")
		return
	}
	path := string(decodedPathBytes)
	rangeHeader := c.Request.Header.Get("Range")

	stream, contentType, _, actualContentLength, filename, httpRespStatus, errService := h.service.GetFileStream(c.Request.Context(), uint(storageID), path, rangeHeader)

	if errService != nil {
		statusToRespond := http.StatusInternalServerError
		if httpRespStatus >= 400 {
			statusToRespond = httpRespStatus
		} else {
			if errors.Is(errService, apperrors.ErrStorageNotFound) || errors.Is(errService, apperrors.ErrFileNotFound) { // Use apperrors
				statusToRespond = http.StatusNotFound
			} else if errors.Is(errService, apperrors.ErrPathIsDir) { // Use apperrors
				statusToRespond = http.StatusBadRequest
			} else if errors.Is(errService, apperrors.ErrRangeNotSatisfiable) { // Use apperrors
				statusToRespond = http.StatusRequestedRangeNotSatisfiable
			}
		}
		h.logger.ErrorContext(c.Request.Context(), "Failed to get file stream for proxy", "storage_id", storageID, "path", path, "error", errService, "reported_status", httpRespStatus)
		common.SendErrorResponse(c, statusToRespond, errService.Error())
		return
	}
	defer stream.Close()

	c.Header("Content-Type", contentType)
	c.Header("Accept-Ranges", "bytes")

	// Set Content-Disposition header with filename
	disposition := fmt.Sprintf("inline; filename=\"%s\"", filename)
	c.Header("Content-Disposition", disposition)

	if actualContentLength > 0 {
		c.Header("Content-Length", strconv.FormatInt(actualContentLength, 10))
	}

	c.Writer.WriteHeader(httpRespStatus)

	_, copyErr := io.Copy(c.Writer, stream)
	if copyErr != nil {
		h.logger.ErrorContext(c.Request.Context(), "Error streaming file to client", "storage_id", storageID, "path", path, "error", copyErr)
	}
}
