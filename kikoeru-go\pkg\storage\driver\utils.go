package driver

import (
	"context"
	"errors"
	"fmt"
	"path/filepath"
	"reflect"
	"strconv"
	"strings"
	// "log/slog" // Add if logging is needed within this utility
)

// GetItemRecursive attempts to retrieve an AlistObj.
// It first tries the optional Getter interface on the driver.
// If the driver doesn't implement Getter or Get returns ErrOperationNotSupported,
// it falls back to a List-based approach to find the item by path.
// Note: The List-based fallback assumes pathOrID is a path.
func GetItemRecursive(ctx context.Context, sd StorageDriver, pathOrID string) (AlistObj, error) {
	// logger := slog.Default().With("util_func", "GetItemRecursive") // Example if logging is desired
	// logger.DebugContext(ctx, "GetItemRecursive called", "path_or_id", pathOrID)

	if getter, ok := sd.(Getter); ok {
		// logger.DebugContext(ctx, "Driver implements Getter, attempting direct Get", "path_or_id", pathOrID)
		obj, err := getter.Get(ctx, pathOrID)
		if err == nil {
			// logger.DebugContext(ctx, "Direct Get successful", "obj_name", obj.GetName())
			return obj, nil
		}
		if !errors.Is(err, ErrOperationNotSupported) {
			// logger.ErrorContext(ctx, "Direct Get failed with an error other than NotSupported", "path_or_id", pathOrID, "error", err)
			return nil, fmt.Errorf("driver Get for '%s' failed: %w", pathOrID, err)
		}
		// logger.DebugContext(ctx, "Direct Get returned ErrOperationNotSupported, falling back to List-based", "path_or_id", pathOrID)
	} else {
		// logger.DebugContext(ctx, "Driver does not implement Getter, using List-based", "path_or_id", pathOrID)
	}

	// Fallback to List-based retrieval (assumes pathOrID is a path for this fallback)
	currentPath := pathOrID
	cleanPath := filepath.Clean(currentPath)

	if currentPath == "/" || (strings.HasSuffix(currentPath, "/") && cleanPath != ".") {
		// Path is already a directory or root
	} else if cleanPath == "." || cleanPath == "" {
		currentPath = "/"
	} else {
		currentPath = cleanPath
	}

	if currentPath == "/" {
		return NewPathAlistObj("/"), nil
	}

	parentDir := filepath.ToSlash(filepath.Dir(currentPath))
	targetName := filepath.Base(currentPath)

	if parentDir == "." {
		parentDir = "/"
	}

	parentAlistObj := NewPathAlistObj(parentDir)

	items, err := sd.List(ctx, parentAlistObj, AlistListArgs{Refresh: false})
	if err != nil {
		return nil, fmt.Errorf("driver error listing parent directory '%s' for fallback Get: %w", parentDir, err)
	}

	for _, item := range items {
		if item.GetName() == targetName {
			return item, nil
		}
	}
	return nil, ErrFileNotFound
}

// ParamsFromStruct generates a slice of ParamInfo by reflecting over the fields of a given struct.
// It uses struct field tags to populate ParamInfo fields.
// Supported tags:
// - `json` or `mapstructure`: for ParamInfo.Name (uses `json` first, then `mapstructure`)
// - `label`: for ParamInfo.Label
// - `description`: for ParamInfo.Description
// - `type`: for ParamInfo.Type (e.g., "string", "bool", "int", "password", "textarea")
// - `required`: "true" or "false" for ParamInfo.Required
// - `default`: string representation of the default value for ParamInfo.DefaultValue
// - `secret`: "true" or "false" for ParamInfo.Secret
// - `options`: comma-separated string for ParamInfo.Options (for "select" type)
func ParamsFromStruct(s interface{}) []ParamInfo {
	var params []ParamInfo
	val := reflect.ValueOf(s)
	if val.Kind() == reflect.Ptr {
		val = val.Elem()
	}
	// Ensure we are dealing with a struct
	if val.Kind() != reflect.Struct {
		return params // Or return an error
	}
	typ := val.Type()

	for i := 0; i < val.NumField(); i++ {
		field := typ.Field(i)
		// Skip unexported fields
		if field.PkgPath != "" {
			continue
		}

		// Handle embedded structs by recursively calling ParamsFromStruct
		if field.Anonymous && field.Type.Kind() == reflect.Struct {
			// Ensure the embedded field is addressable to pass to ParamsFromStruct if it expects an interface
			// or directly pass the interface if the field itself is an interface that can be type asserted.
			// For simplicity, assuming direct struct embedding here.
			embeddedParams := ParamsFromStruct(val.Field(i).Interface())
			params = append(params, embeddedParams...)
			continue
		}

		paramName := field.Tag.Get("json")
		if paramName == "" || paramName == "-" {
			paramName = field.Tag.Get("mapstructure")
		}
		if paramName == "" || paramName == "-" {
			// Fallback to field name if no tags are present, or skip if explicitly ignored
			// For now, let's skip if no specific tag for name is found,
			// or one could use strcase.ToSnake(field.Name)
			continue
		}
		paramName = strings.Split(paramName, ",")[0] // Remove ,omitempty etc.

		param := ParamInfo{
			Name: paramName,
		}

		param.Label = field.Tag.Get("label")
		if param.Label == "" {
			param.Label = strings.Title(strings.ReplaceAll(param.Name, "_", " ")) // Default label from name
		}
		param.Description = field.Tag.Get("description")

		param.Type = field.Tag.Get("type")
		if param.Type == "" { // Infer type if not specified by tag
			switch field.Type.Kind() {
			case reflect.String:
				param.Type = "string"
			case reflect.Bool:
				param.Type = "bool"
			case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64,
				reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
				param.Type = "int" // Consider "number" for web forms
			case reflect.Float32, reflect.Float64:
				param.Type = "float" // Consider "number"
			default:
				param.Type = "string" // Fallback for complex types or unhandled Kinds
			}
		}

		if reqStr := field.Tag.Get("required"); reqStr == "true" {
			param.Required = true
		}

		if defStr := field.Tag.Get("default"); defStr != "" {
			switch param.Type { // Use the determined type for parsing
			case "bool":
				param.DefaultValue, _ = strconv.ParseBool(defStr)
			case "int":
				param.DefaultValue, _ = strconv.ParseInt(defStr, 10, 64)
			case "float":
				param.DefaultValue, _ = strconv.ParseFloat(defStr, 64)
			default: // string and others
				param.DefaultValue = defStr
			}
		}

		if secretStr := field.Tag.Get("secret"); secretStr == "true" {
			param.Secret = true
			if param.Type == "string" { // Often secrets are strings, suggest "password" type for UI
				param.Type = "password"
			}
		}

		if optsStr := field.Tag.Get("options"); optsStr != "" {
			param.Options = strings.Split(optsStr, ",")
			if param.Type == "" { // If type wasn't set, and options are present, assume "select"
				param.Type = "select"
			}
		}
		params = append(params, param)
	}
	return params
}
